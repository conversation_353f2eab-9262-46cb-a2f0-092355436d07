"""
简化的修复测试脚本
直接测试优化配置的效果
"""

import os
import sys
import json
import requests
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_optimized_config():
    """创建优化配置"""
    print("=" * 60)
    print("创建优化配置")
    print("=" * 60)
    
    optimized_config = {
        "api": {
            "base_url": "http://************:3001",
            "key": "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3",
            "model": "rsv-rcgjfz7v",
            "max_tokens": 4000,
            "temperature": 0.7,
            "timeout": 60,
            "max_retries": 3
        },
        "processing": {
            "max_chunk_size": 5000,  # 增加到5000字符
            "questions_per_chunk": 4,  # 减少每块的题目数量
            "single_choice_count": 2,
            "multiple_choice_count": 1,
            "fill_blank_count": 0,  # 暂时禁用
            "short_answer_count": 1,
            "true_false_count": 0,  # 暂时禁用
            "sorting_count": 0,
            "disable_document_splitting": False,
            "enable_chunk_merging": True
        },
        "output": {
            "dir": "test_output"
        }
    }
    
    config_file = "optimized_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, ensure_ascii=False, indent=2)
    
    print(f"[成功] 优化配置已保存: {config_file}")
    print(f"[变更] 主要优化:")
    print(f"  - 分块大小: 3000 -> 5000 字符")
    print(f"  - 每块题目数: 6 -> 4 道")
    print(f"  - 只生成单选题、多选题、简答题")
    
    return optimized_config

def read_document():
    """读取文档"""
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    try:
        doc = Document(doc_path)
        content_parts = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                content_parts.append(text)
        
        for table in doc.tables:
            for row in table.rows:
                row_content = []
                for cell in row.cells:
                    row_content.append(cell.text.strip())
                if any(row_content):
                    content_parts.append(' | '.join(row_content))
        
        content = '\n\n'.join(content_parts)
        return content
        
    except Exception as e:
        print(f"[错误] 读取文档失败: {str(e)}")
        return None

def create_chunks(content, chunk_size=5000):
    """创建文档块"""
    if not content:
        return []
    
    chunks = []
    start = 0
    chunk_index = 0
    
    while start < len(content):
        end = start + chunk_size
        
        # 尝试在句号处分割
        if end < len(content):
            # 向后查找最近的句号
            for i in range(end, min(end + 200, len(content))):
                if content[i] in '。.':
                    end = i + 1
                    break
        
        chunk_content = content[start:end].strip()
        
        if chunk_content:
            chunks.append({
                'chunk_index': chunk_index,
                'content': chunk_content,
                'char_count': len(chunk_content),
                'start_pos': start,
                'end_pos': end
            })
            chunk_index += 1
        
        start = end
    
    return chunks

def test_llm_with_chunk(chunk, config):
    """测试LLM生成"""
    api_url = f"{config['api']['base_url']}/v1/chat/completions"
    api_key = config['api']['key']
    model = config['api']['model']
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 构建提示词
    prompt = f"""请根据以下文档内容生成题目，严格按照新模板.ini格式要求。要求生成以下数量的题目：
- 单选题：{config['processing']['single_choice_count']}道
- 多选题：{config['processing']['multiple_choice_count']}道
- 简答题：{config['processing']['short_answer_count']}道

**重要要求**：
1. 严格基于文档内容，不得编造信息
2. 如果内容不足以生成指定数量的题目，请返回"insufficient_content"
3. 简答题的关键词应该是2-6字的核心概念，不是单个字

**输出格式**：
请直接输出INI格式的题目，格式如下：

1.【单选题】题目内容
A、选项内容
B、选项内容
C、选项内容
D、选项内容
正确答案：A
题目难度：简单
答案解析：根据文档内容...
知识点：知识点1|知识点2

2.【简答题】题目内容
关键字1：核心概念1/主要内容1/重点要素1
关键字2：核心概念2/主要内容2/重点要素2
题目难度：一般
作答上传图片：否
答案解析：根据文档内容...
知识点：知识点1|知识点2

文档内容：
{chunk['content']}
"""
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的题目生成助手。请严格按照要求的格式输出。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "max_tokens": config['api']['max_tokens'],
        "temperature": config['api']['temperature']
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=config['api']['timeout'])
        
        if response.status_code == 200:
            response_json = response.json()
            content_response = response_json['choices'][0]['message']['content']
            
            if "insufficient_content" in content_response.lower():
                return None, "insufficient_content"
            elif "【单选题】" in content_response or "【简答题】" in content_response:
                return content_response, "success"
            else:
                return None, "format_error"
        else:
            return None, f"api_error_{response.status_code}"
            
    except Exception as e:
        return None, f"exception_{str(e)}"

def save_results(results, config):
    """保存结果"""
    if not results:
        return None
    
    output_dir = config['output']['dir']
    os.makedirs(output_dir, exist_ok=True)
    
    # 合并所有结果
    all_content = []
    for i, (chunk_idx, content) in enumerate(results):
        all_content.append(f"# 来源块 {chunk_idx + 1}\n")
        all_content.append(content)
        all_content.append("\n" + "="*40 + "\n")
    
    # 添加填写规范头部
    header = """填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写"对或错"；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用"/"分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；关键词不得相同
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含"/"，请以{/}表示以防止系统误判为答案分隔符。

"""
    
    filename = "输煤运行规程_优化配置测试.ini"
    file_path = os.path.join(output_dir, filename)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(header)
        f.write('\n'.join(all_content))
    
    return file_path

def main():
    """主函数"""
    print("[修复] 660MW超临界机组输煤运行规程 - 优化配置测试")
    
    # 步骤1: 创建优化配置
    config = create_optimized_config()
    
    # 步骤2: 读取文档
    print("\n[步骤1] 读取文档...")
    content = read_document()
    
    if not content:
        print("[错误] 无法读取文档")
        return
    
    print(f"  文档总长度: {len(content)} 字符")
    
    # 步骤3: 创建优化的文档块
    print("\n[步骤2] 创建文档块...")
    chunks = create_chunks(content, config['processing']['max_chunk_size'])
    print(f"  分割后块数: {len(chunks)}")
    
    for i, chunk in enumerate(chunks[:3]):  # 显示前3个块
        print(f"    块{i+1}: {chunk['char_count']} 字符")
    
    # 步骤4: 测试LLM生成
    print("\n[步骤3] 测试LLM生成...")
    successful_results = []
    failed_results = []
    
    # 只测试前3个块
    for i, chunk in enumerate(chunks[:3]):
        print(f"\n  测试块 {i+1}:")
        print(f"    字符数: {chunk['char_count']}")
        
        result, status = test_llm_with_chunk(chunk, config)
        
        if status == "success":
            print(f"    [成功] 生成题目成功")
            successful_results.append((i, result))
            
            # 统计题目数量
            single_count = result.count("【单选题】")
            multiple_count = result.count("【多选题】")
            short_answer_count = result.count("【简答题】")
            print(f"      单选题: {single_count}道")
            print(f"      多选题: {multiple_count}道")
            print(f"      简答题: {short_answer_count}道")
            
        elif status == "insufficient_content":
            print(f"    [失败] LLM报告内容不足")
            failed_results.append((i, status))
        else:
            print(f"    [失败] 其他错误: {status}")
            failed_results.append((i, status))
    
    # 步骤5: 保存结果
    if successful_results:
        print(f"\n[步骤4] 保存结果...")
        file_path = save_results(successful_results, config)
        print(f"  [成功] 结果已保存: {file_path}")
    
    # 总结
    print(f"\n[总结] 测试结果:")
    print(f"  成功块数: {len(successful_results)}")
    print(f"  失败块数: {len(failed_results)}")
    
    if len(successful_results) > 0:
        print(f"\n[成功] 优化配置有效！")
        print(f"[建议] 可以使用以下配置:")
        print(f"  - 分块大小: {config['processing']['max_chunk_size']} 字符")
        print(f"  - 每块题目数: {config['processing']['questions_per_chunk']} 道")
        print(f"  - 题型配置: 单选{config['processing']['single_choice_count']}道, 多选{config['processing']['multiple_choice_count']}道, 简答{config['processing']['short_answer_count']}道")
    else:
        print(f"\n[失败] 优化配置仍然无效")
        print(f"[建议] 需要进一步调整:")
        print(f"  1. 增加分块大小到8000-10000字符")
        print(f"  2. 减少每块题目数量到2-3道")
        print(f"  3. 检查API配置和网络连接")
    
    print(f"\n[完成] 测试完成")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
