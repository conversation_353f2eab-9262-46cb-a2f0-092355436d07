"""
INI输出管理模块
负责将生成的题目保存为INI格式，严格按照新模板.ini格式要求
"""

import os
import logging
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class INIWriter:
    """INI文件写入器，按照新模板.ini格式"""

    def __init__(self, config):
        """
        初始化INI写入器

        Args:
            config: 配置对象
        """
        self.config = config
        self.output_dir = config.OUTPUT_DIR

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        logger.info(f"INI写入器初始化完成，输出目录: {self.output_dir}")

    def save_questions_to_ini(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        保存题目到INI文件

        Args:
            questions: 题目列表
            filename: 输出文件名，默认使用时间戳

        Returns:
            保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"quiz_results_{timestamp}.ini"

        file_path = os.path.join(self.output_dir, filename)

        try:
            # 生成INI格式内容
            ini_content = self._generate_ini_content(questions)

            # 写入INI文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(ini_content)

            logger.info(f"成功保存 {len(questions)} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"保存INI文件失败: {str(e)}")
            raise

    def append_questions_to_ini(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        追加题目到现有INI文件

        Args:
            questions: 题目列表
            filename: 文件名

        Returns:
            文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"quiz_results_{timestamp}.ini"

        file_path = os.path.join(self.output_dir, filename)

        try:
            # 生成新题目的INI内容
            new_content = self._generate_questions_only_content(questions)

            # 检查文件是否存在
            if os.path.exists(file_path):
                # 追加到现有文件
                with open(file_path, 'a', encoding='utf-8') as f:
                    f.write("\n\n" + new_content)
            else:
                # 创建新文件，包含填写规范
                ini_content = self._generate_ini_content(questions)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(ini_content)

            logger.info(f"成功追加 {len(questions)} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"追加INI文件失败: {str(e)}")
            raise

    def _generate_ini_content(self, questions: List[Dict[str, Any]]) -> str:
        """
        生成完整的INI文件内容，包含填写规范

        Args:
            questions: 题目列表

        Returns:
            INI格式的完整内容
        """
        # 填写规范头部
        header = """填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写"对或错"；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用"/"分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；关键词不得相同
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含"/"，请以{/}表示以防止系统误判为答案分隔符。示例，如a/b{/}b/c{/}{/}c,表示有三个备选答案"a"、"b/b"、"c//c"。

"""

        # 生成题目内容
        questions_content = self._generate_questions_only_content(questions)

        return header + questions_content

    def _generate_questions_only_content(self, questions: List[Dict[str, Any]]) -> str:
        """
        生成题目内容（不包含填写规范）

        Args:
            questions: 题目列表

        Returns:
            题目的INI格式内容
        """
        content_parts = []
        question_number = 1

        for question in questions:
            try:
                question_content = self._format_single_question(question, question_number)
                if question_content:
                    content_parts.append(question_content)
                    question_number += 1
            except Exception as e:
                logger.error(f"格式化题目失败: {str(e)}")
                continue

        return "\n\n".join(content_parts)

    def _format_single_question(self, question: Dict[str, Any], question_number: int) -> str:
        """
        格式化单个题目为INI格式

        Args:
            question: 题目数据
            question_number: 题目编号

        Returns:
            格式化后的题目内容
        """
        question_type = question.get('type', '').strip()
        question_text = question.get('question', '').strip()
        difficulty = question.get('difficulty', '简单').strip()
        explanation = question.get('explanation', '暂无解析').strip()
        knowledge_points = question.get('knowledge_points', [])

        # 处理知识点
        if isinstance(knowledge_points, str):
            knowledge_points = [kp.strip() for kp in knowledge_points.split('|') if kp.strip()]
        elif not isinstance(knowledge_points, list):
            knowledge_points = []
        
        knowledge_points_str = '|'.join(knowledge_points[:5]) if knowledge_points else '暂无知识点'

        # 映射题型名称
        type_mapping = {
            '单选': '单选题',
            '多选': '多选题', 
            '判断': '判断题',
            '填空': '填空题',
            '问答': '简答题',
            '简答': '简答题'
        }
        formatted_type = type_mapping.get(question_type, question_type)

        if formatted_type == '单选题':
            return self._format_single_choice(question, question_number, question_text, difficulty, explanation, knowledge_points_str)
        elif formatted_type == '多选题':
            return self._format_multiple_choice(question, question_number, question_text, difficulty, explanation, knowledge_points_str)
        elif formatted_type == '判断题':
            return self._format_true_false(question, question_number, question_text, difficulty, explanation, knowledge_points_str)
        elif formatted_type == '填空题':
            return self._format_fill_blank(question, question_number, question_text, difficulty, explanation, knowledge_points_str)
        elif formatted_type == '简答题':
            return self._format_short_answer(question, question_number, question_text, difficulty, explanation, knowledge_points_str)
        else:
            logger.warning(f"未知题型: {question_type}")
            return ""

    def _format_single_choice(self, question: Dict[str, Any], num: int, text: str, difficulty: str, explanation: str, knowledge: str) -> str:
        """格式化单选题"""
        options = question.get('options', '')
        answer = question.get('answer', 'A')

        # 解析选项
        if isinstance(options, str):
            option_list = [opt.strip() for opt in options.split('|') if opt.strip()]
        else:
            option_list = []

        # 构建选项文本
        option_lines = []
        for i, option in enumerate(option_list[:12]):  # 最多12个选项
            letter = chr(65 + i)  # A, B, C, D...
            option_lines.append(f"{letter}、{option}")

        options_text = '\n'.join(option_lines)

        return f"""{num}.【单选题】{text}
{options_text}
正确答案：{answer}
题目难度：{difficulty}
答案解析：{explanation}
知识点：{knowledge}"""

    def _format_multiple_choice(self, question: Dict[str, Any], num: int, text: str, difficulty: str, explanation: str, knowledge: str) -> str:
        """格式化多选题"""
        options = question.get('options', '')
        answer = question.get('answer', 'AB')

        # 解析选项
        if isinstance(options, str):
            option_list = [opt.strip() for opt in options.split('|') if opt.strip()]
        else:
            option_list = []

        # 构建选项文本
        option_lines = []
        for i, option in enumerate(option_list[:12]):  # 最多12个选项
            letter = chr(65 + i)  # A, B, C, D...
            option_lines.append(f"{letter}、{option}")

        options_text = '\n'.join(option_lines)

        return f"""{num}.【多选题】{text}
{options_text}
正确答案：{answer}
题目难度：{difficulty}
答案解析：{explanation}
知识点：{knowledge}"""

    def _format_true_false(self, question: Dict[str, Any], num: int, text: str, difficulty: str, explanation: str, knowledge: str) -> str:
        """格式化判断题"""
        answer = question.get('answer', '对')
        
        # 标准化答案格式
        if answer.lower() in ['true', '正确', '是', '对']:
            answer = '对'
        else:
            answer = '错'

        return f"""{num}.【判断题】{text}
正确答案：{answer}
题目难度：{difficulty}
答案解析：{explanation}
知识点：{knowledge}"""

    def _format_fill_blank(self, question: Dict[str, Any], num: int, text: str, difficulty: str, explanation: str, knowledge: str) -> str:
        """格式化填空题"""
        # 填空题的特殊处理，需要将答案嵌入到题目中
        answer = question.get('answer', '')
        
        # 如果答案包含多个空的答案，用|分隔
        if isinstance(answer, str) and '|' in answer:
            answers = [ans.strip() for ans in answer.split('|') if ans.strip()]
        else:
            answers = [str(answer)] if answer else ['答案']

        # 为每个答案生成备选项（简化处理，每个答案作为一个备选项）
        formatted_answers = []
        for ans in answers:
            # 为每个答案生成2-3个备选项
            formatted_answers.append(f"【{ans}/{ans}/{ans}】")

        # 如果题目中没有【】标记，则在适当位置添加
        if '【' not in text:
            # 简单处理：在题目末尾添加空白
            formatted_text = text + "".join(formatted_answers)
        else:
            formatted_text = text

        return f"""{num}.【填空题】{formatted_text}
题目难度：{difficulty}
作答上传图片：否
答案解析：{explanation}
知识点：{knowledge}"""

    def _format_short_answer(self, question: Dict[str, Any], num: int, text: str, difficulty: str, explanation: str, knowledge: str) -> str:
        """格式化简答题"""
        answer = question.get('answer', '')
        
        # 将答案转换为关键词格式
        keywords = self._extract_keywords_from_answer(answer)
        
        keyword_lines = []
        for i, (keyword, alternatives) in enumerate(keywords[:12], 1):  # 最多12个关键词
            keyword_lines.append(f"关键字{i}：{'/'.join(alternatives)}")

        keywords_text = '\n'.join(keyword_lines)

        return f"""{num}.【简答题】{text}
{keywords_text}
题目难度：{difficulty}
作答上传图片：否
答案解析：{explanation}
知识点：{knowledge}"""

    def _extract_keywords_from_answer(self, answer: str) -> List[tuple]:
        """
        从答案中提取关键词和备选答案
        
        Args:
            answer: 原始答案
            
        Returns:
            关键词和备选答案的列表，格式为[(关键词, [备选答案列表]), ...]
        """
        if not answer:
            return [("核心要点", ["要点", "重点", "关键"])]

        # 简化处理：将答案按句号或逗号分割，提取关键概念
        import re
        
        # 分割答案
        sentences = re.split(r'[。，；,;]', answer)
        keywords = []
        
        for i, sentence in enumerate(sentences[:6]):  # 最多6个关键词
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # 提取关键词（简化处理）
            if len(sentence) > 10:
                # 长句子，取前几个字作为关键词
                keyword = sentence[:6].strip()
            else:
                keyword = sentence
                
            # 生成备选答案
            alternatives = [keyword]
            if len(keyword) > 2:
                alternatives.append(keyword[:4])
                alternatives.append(keyword[:-1] if len(keyword) > 3 else keyword)
            else:
                alternatives.extend([keyword, keyword])
                
            keywords.append((keyword, alternatives[:3]))
        
        # 如果没有提取到关键词，提供默认值
        if not keywords:
            keywords = [("核心要点", ["要点", "重点", "关键"])]
            
        return keywords

    def create_progress_backup(self, questions: List[Dict[str, Any]], backup_suffix: str = None) -> str:
        """
        创建INI备份文件

        Args:
            questions: 题目列表
            backup_suffix: 备份文件后缀

        Returns:
            备份文件路径
        """
        if not questions:
            logger.info("没有题目可供备份，跳过备份创建。")
            return ""

        if backup_suffix is None:
            backup_suffix = datetime.now().strftime("%Y%m%d%H%M%S")
        
        backup_filename = f"quiz_backup_{backup_suffix}.ini"
        return self.save_questions_to_ini(questions, backup_filename)

    def generate_summary_report(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成处理摘要报告

        Args:
            questions: 已处理的题目列表

        Returns:
            包含各类题目数量的字典
        """
        summary = {
            "total_questions": len(questions),
            "单选题": 0,
            "多选题": 0,
            "判断题": 0,
            "填空题": 0,
            "简答题": 0
        }

        type_mapping = {
            '单选': '单选题',
            '多选': '多选题', 
            '判断': '判断题',
            '填空': '填空题',
            '问答': '简答题',
            '简答': '简答题'
        }

        for q in questions:
            q_type = q.get('type', '')
            mapped_type = type_mapping.get(q_type, q_type)
            if mapped_type in summary:
                summary[mapped_type] += 1

        return summary
