"""
OpenAI兼容API客户端
支持自定义API端点和参数
"""

import json
import time
import logging
import threading
from typing import Dict, List, Optional, Any
from openai import OpenAI
import tiktoken

logger = logging.getLogger(__name__)

class OpenAICompatibleClient:
    """OpenAI兼容的API客户端"""

    def __init__(self, config):
        """
        初始化客户端

        Args:
            config: 配置对象，包含API设置
        """
        self.config = config

        # 确保API_BASE_URL以/v1结尾
        base_url = config.API_BASE_URL
        if not base_url.endswith('/v1'):
            if base_url.endswith('/'):
                base_url = base_url + 'v1'
            else:
                base_url = base_url + '/v1'

        self.client = OpenAI(
            api_key=config.API_KEY,
            base_url=base_url
        )

        # 验证配置
        if not config.API_KEY:
            raise ValueError("API_KEY不能为空")

        # API速率限制相关
        self.last_api_call_time = 0
        self.api_call_lock = threading.Lock()  # 确保API调用的同步
        self.min_interval = 1  # 最小间隔1秒

        self.encoding = tiktoken.get_encoding(config.ENCODING_NAME)

        logger.info(f"初始化OpenAI兼容客户端，API端点: {config.API_BASE_URL}")
        logger.info(f"API请求最小间隔: {self.min_interval}秒")
        print(f"当前LLM客户端加载的MAX_TOKENS配置为: {self.config.MAX_TOKENS}")

    def generate_quiz(self, content: str, num_questions: int = None, source_filename: str = "未知文档",
                     question_counts: Dict[str, int] = None) -> Dict[str, Any]:
        """
        根据内容生成题目

        Args:
            content: 文档内容
            num_questions: 生成题目数量，默认使用配置中的值
            source_filename: 源文件名
            question_counts: 各类题目数量字典

        Returns:
            生成的题目数据，如果内容不足则返回None
        """
        if num_questions is None:
            num_questions = self.config.QUESTIONS_PER_CHUNK

        # 预检查内容是否足够生成题目
        if not self._validate_content_sufficiency(content):
            logger.warning("文档内容不足以生成高质量题目")
            return None

        # 使用传入的题目数量或配置中的默认值
        if question_counts:
            single_choice_count = question_counts.get('single_choice_count', self.config.SINGLE_CHOICE_COUNT)
            multiple_choice_count = question_counts.get('multiple_choice_count', self.config.MULTIPLE_CHOICE_COUNT)
            fill_blank_count = question_counts.get('fill_blank_count', self.config.FILL_BLANK_COUNT)
            short_answer_count = question_counts.get('short_answer_count', self.config.SHORT_ANSWER_COUNT)
            true_false_count = question_counts.get('true_false_count', self.config.TRUE_FALSE_COUNT)
            sorting_count = question_counts.get('sorting_count', self.config.SORTING_COUNT)
        else:
            single_choice_count = self.config.SINGLE_CHOICE_COUNT
            multiple_choice_count = self.config.MULTIPLE_CHOICE_COUNT
            fill_blank_count = self.config.FILL_BLANK_COUNT
            short_answer_count = self.config.SHORT_ANSWER_COUNT
            true_false_count = self.config.TRUE_FALSE_COUNT
            sorting_count = self.config.SORTING_COUNT

        prompt = self.config.QUIZ_PROMPT_TEMPLATE.format(
            single_choice_count=single_choice_count,
            multiple_choice_count=multiple_choice_count,
            fill_blank_count=fill_blank_count,
            short_answer_count=short_answer_count,
            true_false_count=true_false_count,
            sorting_count=sorting_count,
            source_filename=source_filename,
            content=content
        )

        for attempt in range(self.config.MAX_RETRIES):
            try:
                response = self._make_api_call(prompt)

                # 解析响应
                quiz_data = self._parse_response(response)
                if quiz_data is None:
                    logger.warning(f"generate_quiz: _parse_response返回None，尝试 {attempt + 1}/{self.config.MAX_RETRIES}。原始响应前200字符: {response[:200] if response else '无响应'}")
                    # 如果是解析失败导致None，且不是最后一次尝试，可以重试
                    if attempt < self.config.MAX_RETRIES - 1:
                        time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                        continue # 继续下一次尝试
                    else:
                        logger.error("generate_quiz: 达到最大重试次数，且响应解析失败")
                        return None

                # 检查是否返回了内容不足的标志
                if quiz_data.get('insufficient_content'):
                    logger.warning("generate_quiz: LLM报告文档内容不足以生成题目")
                    return None

                if 'questions' in quiz_data:
                    # 验证生成的题目质量
                    validation_result = self._validate_questions(quiz_data['questions'], content)
                    if validation_result['has_valid_questions']:
                        logger.info(f"generate_quiz: 成功生成并验证 {len(validation_result['validated_questions'])} 道题目")
                        if validation_result['filtered_count'] > 0:
                            logger.info(f"generate_quiz: 过滤掉 {validation_result['filtered_count']} 道题目，保留 {len(validation_result['validated_questions'])} 道题目")
                        return {'questions': validation_result['validated_questions']}
                    else:
                        logger.warning("generate_quiz: 生成的题目全部被过滤或未通过质量验证")
                        return None
                else:
                    logger.warning(f"generate_quiz: API响应格式不正确（缺少'questions'键），尝试 {attempt + 1}/{self.config.MAX_RETRIES}。响应内容前200字符: {str(quiz_data)[:200]}")

            except Exception as e:
                logger.error(f"generate_quiz: API调用失败 (尝试 {attempt + 1}/{self.config.MAX_RETRIES}): {str(e)}")

                if attempt < self.config.MAX_RETRIES - 1:
                    time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                else:
                    logger.error("generate_quiz: 达到最大重试次数，题目生成失败")
                    return None

        logger.error("generate_quiz: 达到最大重试次数，题目生成失败")
        return None

    def _make_api_call(self, prompt: str) -> str:
        """
        调用API，带速率限制和同步等待

        Args:
            prompt: 提示词

        Returns:
            API响应内容
        """
        with self.api_call_lock:  # 确保API调用的同步
            # 计算需要等待的时间
            current_time = time.time()
            elapsed = current_time - self.last_api_call_time
            wait_time = max(0, self.min_interval - elapsed)

            if wait_time > 0:
                logger.debug(f"API速率限制：等待 {wait_time:.2f} 秒")
                time.sleep(wait_time)

            try:
                # 记录API调用开始时间
                call_start_time = time.time()
                logger.debug(f"开始API调用，距离上次调用间隔: {call_start_time - self.last_api_call_time:.2f} 秒")

                response = self.client.chat.completions.create(
                    model=self.config.MODEL_NAME,
                    messages=[
                        {
                            "role": "system",
                            "content": """你是一个专业的题目生成助手。请严格遵循以下原则：

1. **严格基于文档内容**：只能基于提供的文档内容生成题目，绝对不能编造或添加文档中没有的信息
2. **内容不足时拒绝**：如果文档内容不足以生成高质量题目，请返回"insufficient_content"
3. **质量优于数量**：宁可生成较少的高质量题目，也不要生成不准确的题目
4. **可追溯性**：每个题目都应该能在原文档中找到对应的依据
5. **格式严格**：必须按照要求的格式输出

如果无法满足要求，请诚实地返回失败响应。"""
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    max_tokens=self.config.MAX_TOKENS,
                    temperature=self.config.TEMPERATURE,
                    timeout=self.config.REQUEST_TIMEOUT
                )

                # 更新最后调用时间
                self.last_api_call_time = time.time()
                call_duration = self.last_api_call_time - call_start_time
                logger.debug(f"API调用完成，耗时: {call_duration:.2f} 秒")

                # 处理不同的响应格式
                if isinstance(response, str):
                    # 如果响应直接是字符串
                    return response
                elif hasattr(response, 'choices') and response.choices:
                    # 标准OpenAI格式
                    return response.choices[0].message.content
                elif hasattr(response, 'content'):
                    # 其他格式
                    return response.content
                else:
                    # 尝试转换为字符串
                    return str(response)

            except Exception as e:
                # 即使失败也要更新时间，避免频繁重试
                self.last_api_call_time = time.time()
                logger.error(f"API调用异常: {str(e)}")
                raise

    def _parse_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        解析API响应，支持INI格式和JSON格式

        Args:
            response_text: API响应文本

        Returns:
            解析后的题目数据
        """
        # 检查是否返回内容不足标志
        if "insufficient_content" in response_text.lower():
            return {"insufficient_content": True}

        # 首先尝试解析INI格式（新模板格式）
        ini_result = self._parse_ini_response(response_text)
        if ini_result:
            logger.debug("_parse_response: INI格式解析成功")
            return ini_result

        # 如果INI解析失败，尝试JSON格式（向后兼容）
        try:
            # 尝试直接解析JSON
            parsed_json = json.loads(response_text)
            logger.debug("_parse_response: JSON格式直接解析成功")
            return parsed_json

        except json.JSONDecodeError as e:
            # 定义一个辅助函数来尝试修复和解析JSON
            def try_parse_partial_json(json_string):
                # 尝试查找最后一个完整的JSON对象或数组

                # 尝试解析为对象
                brace_index = json_string.rfind('}')
                if brace_index != -1:
                    potential_json = json_string[:brace_index + 1]
                    try:
                        return json.loads(potential_json), "object"
                    except json.JSONDecodeError:
                        pass

                # 尝试解析为数组
                bracket_index = json_string.rfind(']')
                if bracket_index != -1:
                    potential_json = json_string[:bracket_index + 1]
                    try:
                        return json.loads(potential_json), "array"
                    except json.JSONDecodeError:
                        pass

                return None, None

            # 优先查找Markdown JSON块
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                extracted_content = json_match.group(1)
                logger.debug(f"_parse_response: 尝试从markdown块中解析JSON内容: {extracted_content[:200]}...")
                try:
                    extracted_json = json.loads(extracted_content)
                    logger.debug("_parse_response: 提取Markdown JSON块成功")
                    return extracted_json
                except json.JSONDecodeError as inner_e:
                    # 尝试修复Markdown块内的JSON
                    fixed_json, json_type = try_parse_partial_json(extracted_content)
                    if fixed_json:
                        logger.info(f"_parse_response: 成功修复并解析Markdown块内的部分JSON ({json_type})")
                        return fixed_json

            # 如果没有Markdown JSON块，或Markdown块内的JSON无法修复，则尝试从整个响应中提取并修复
            fixed_json, json_type = try_parse_partial_json(response_text)
            if fixed_json:
                logger.info(f"_parse_response: 成功修复并解析响应中的部分JSON ({json_type})")
                return fixed_json
            else:
                logger.warning("_parse_response: 无法解析响应为JSON或INI格式")
                return None

        except Exception as e:
            logger.error(f"_parse_response: 解析响应时发生异常: {str(e)}")
            return None

    def _parse_ini_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        解析INI格式的响应

        Args:
            response_text: INI格式的响应文本

        Returns:
            解析后的题目数据
        """
        try:
            import re

            # 查找所有题目
            questions = []

            # 匹配题目模式：数字.【题型】题目内容
            question_pattern = r'(\d+)\.【(.*?)】(.*?)(?=\d+\.【|$)'
            matches = re.findall(question_pattern, response_text, re.DOTALL)

            for match in matches:
                question_num, question_type, content = match
                question_type = question_type.strip()
                content = content.strip()

                try:
                    parsed_question = self._parse_single_ini_question(question_type, content)
                    if parsed_question:
                        questions.append(parsed_question)
                except Exception as e:
                    logger.warning(f"解析第{question_num}题失败: {str(e)}")
                    continue

            if questions:
                return {"questions": questions}
            else:
                return None

        except Exception as e:
            logger.debug(f"_parse_ini_response: INI解析失败: {str(e)}")
            return None

    def _parse_single_ini_question(self, question_type: str, content: str) -> Optional[Dict[str, Any]]:
        """
        解析单个INI格式题目

        Args:
            question_type: 题目类型
            content: 题目内容

        Returns:
            解析后的题目数据
        """
        import re

        # 提取题目文本（第一行）
        lines = content.split('\n')
        question_text = lines[0].strip()

        # 初始化题目数据
        question_data = {
            'type': question_type.replace('题', ''),  # 单选题 -> 单选
            'question': question_text,
            'difficulty': '简单',
            'explanation': '暂无解析',
            'knowledge_points': []
        }

        # 解析不同类型的题目
        if question_type in ['单选题', '多选题']:
            return self._parse_choice_question(question_data, content)
        elif question_type == '判断题':
            return self._parse_true_false_question(question_data, content)
        elif question_type == '填空题':
            return self._parse_fill_blank_question(question_data, content)
        elif question_type == '简答题':
            return self._parse_short_answer_question(question_data, content)
        else:
            logger.warning(f"未知题型: {question_type}")
            return None

    def _parse_choice_question(self, question_data: Dict[str, Any], content: str) -> Dict[str, Any]:
        """解析选择题"""
        import re

        lines = content.split('\n')
        options = []

        # 提取选项
        for line in lines[1:]:
            line = line.strip()
            if re.match(r'^[A-L]、', line):
                option_text = line[2:].strip()  # 去掉"A、"部分
                options.append(option_text)
            elif line.startswith('正确答案：'):
                question_data['answer'] = line.replace('正确答案：', '').strip()
            elif line.startswith('题目难度：'):
                question_data['difficulty'] = line.replace('题目难度：', '').strip()
            elif line.startswith('答案解析：'):
                question_data['explanation'] = line.replace('答案解析：', '').strip()
            elif line.startswith('知识点：'):
                knowledge_str = line.replace('知识点：', '').strip()
                question_data['knowledge_points'] = [kp.strip() for kp in knowledge_str.split('|') if kp.strip()]

        question_data['options'] = '|'.join(options)
        return question_data

    def _parse_true_false_question(self, question_data: Dict[str, Any], content: str) -> Dict[str, Any]:
        """解析判断题"""
        lines = content.split('\n')

        for line in lines[1:]:
            line = line.strip()
            if line.startswith('正确答案：'):
                answer = line.replace('正确答案：', '').strip()
                # 标准化答案格式
                if answer in ['对', '正确', 'True']:
                    question_data['answer'] = '正确'
                else:
                    question_data['answer'] = '错误'
            elif line.startswith('题目难度：'):
                question_data['difficulty'] = line.replace('题目难度：', '').strip()
            elif line.startswith('答案解析：'):
                question_data['explanation'] = line.replace('答案解析：', '').strip()
            elif line.startswith('知识点：'):
                knowledge_str = line.replace('知识点：', '').strip()
                question_data['knowledge_points'] = [kp.strip() for kp in knowledge_str.split('|') if kp.strip()]

        question_data['options'] = ''
        return question_data

    def _parse_fill_blank_question(self, question_data: Dict[str, Any], content: str) -> Dict[str, Any]:
        """解析填空题"""
        import re
        lines = content.split('\n')

        # 从题目文本中提取答案
        question_text = lines[0].strip()

        # 查找【答案1/答案2/答案3】格式的答案
        answer_pattern = r'【([^】]+)】'
        answers = re.findall(answer_pattern, question_text)

        # 处理答案，取每组的第一个作为主答案
        processed_answers = []
        for answer_group in answers:
            first_answer = answer_group.split('/')[0].strip()
            processed_answers.append(first_answer)

        question_data['answer'] = '|'.join(processed_answers) if processed_answers else ''

        # 解析其他字段
        for line in lines[1:]:
            line = line.strip()
            if line.startswith('题目难度：'):
                question_data['difficulty'] = line.replace('题目难度：', '').strip()
            elif line.startswith('答案解析：'):
                question_data['explanation'] = line.replace('答案解析：', '').strip()
            elif line.startswith('知识点：'):
                knowledge_str = line.replace('知识点：', '').strip()
                question_data['knowledge_points'] = [kp.strip() for kp in knowledge_str.split('|') if kp.strip()]

        question_data['options'] = ''
        return question_data

    def _parse_short_answer_question(self, question_data: Dict[str, Any], content: str) -> Dict[str, Any]:
        """解析简答题"""
        import re
        lines = content.split('\n')

        keywords = []

        for line in lines[1:]:
            line = line.strip()
            if re.match(r'^关键字\d+：', line):
                # 提取关键词和备选答案
                keyword_content = re.sub(r'^关键字\d+：', '', line)
                alternatives = [alt.strip() for alt in keyword_content.split('/') if alt.strip()]
                if alternatives:
                    keywords.extend(alternatives)  # 将所有备选答案作为关键词
            elif line.startswith('题目难度：'):
                question_data['difficulty'] = line.replace('题目难度：', '').strip()
            elif line.startswith('答案解析：'):
                question_data['explanation'] = line.replace('答案解析：', '').strip()
            elif line.startswith('知识点：'):
                knowledge_str = line.replace('知识点：', '').strip()
                question_data['knowledge_points'] = [kp.strip() for kp in knowledge_str.split('|') if kp.strip()]

        # 将关键词组合成答案
        question_data['answer'] = '；'.join(keywords) if keywords else '请根据文档内容回答'
        question_data['options'] = ''
        return question_data

    def test_connection(self) -> bool:
        """
        测试API连接

        Returns:
            连接是否成功
        """
        try:
            test_prompt = "请回复'连接测试成功'"
            response = self._make_api_call(test_prompt)

            if response and len(response.strip()) > 0:
                logger.info("API连接测试成功")
                return True
            else:
                logger.error("API连接测试失败：响应为空")
                return False

        except Exception as e:
            logger.error(f"API连接测试失败: {str(e)}")
            return False

    def batch_generate_quiz(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量生成题目

        Args:
            chunks: 文本块列表

        Returns:
            生成的题目列表
        """
        all_questions = []
        failed_chunks = []

        for i, chunk in enumerate(chunks):
            try:
                logger.info(f"处理文本块 {i+1}/{len(chunks)}: {chunk['filename']}")

                quiz_data = self.generate_quiz(
                    content=chunk['content'],
                    source_filename=chunk.get('filename', '未知文档')
                )

                if quiz_data and 'questions' in quiz_data:
                    # 为每个题目添加来源信息
                    for question in quiz_data['questions']:
                        question['source_file'] = chunk['filename']
                        question['chunk_index'] = chunk['chunk_index']
                        all_questions.append(question)
                else:
                    logger.warning(f"文本块 {i+1} 生成题目失败")
                    failed_chunks.append(chunk)

                # 不需要额外延迟，API调用本身已经有速率限制

            except Exception as e:
                logger.error(f"处理文本块 {i+1} 失败: {str(e)}")
                failed_chunks.append(chunk)
                continue

        logger.info(f"批量生成完成，成功生成 {len(all_questions)} 道题目，失败 {len(failed_chunks)} 个文本块")

        return all_questions, failed_chunks

    def _validate_content_sufficiency(self, content: str) -> bool:
        """
        验证内容是否足够生成题目

        Args:
            content: 文档内容

        Returns:
            内容是否充分
        """
        if not content or len(content.strip()) < 50:
            return False

        # 检查内容是否包含足够的信息
        words = content.split()
        if len(words) < 20:
            return False

        # 检查是否包含实质性内容（不只是标点符号和空白）
        meaningful_chars = sum(1 for c in content if c.isalnum() or c.isspace())
        if meaningful_chars < len(content) * 0.7:
            return False

        return True

    def _validate_questions(self, questions: List[Dict], original_content: str = None) -> Dict[str, Any]:
        """
        验证和过滤题目

        Args:
            questions: 生成的题目列表
            original_content: 原始文档内容

        Returns:
            验证结果字典，包含:
            - has_valid_questions: 是否有有效题目
            - validated_questions: 过滤后的题目列表
            - filtered_count: 被过滤的题目数量
            - original_count: 原始题目数量
        """
        if not self.config.ENABLE_QUESTION_FILTERING:
            return {
                'has_valid_questions': len(questions) > 0,
                'validated_questions': questions,
                'filtered_count': 0,
                'original_count': len(questions)
            }

        filtered_questions = []
        filtered_count = 0

        for question in questions:
            # 确保题目是字典类型，避免NoneType错误
            if not isinstance(question, dict):
                logger.warning(f"_validate_questions: 检测到非字典格式题目或None值，跳过: {question}")
                filtered_count += 1
                continue

            if self._should_filter_question(question):
                filtered_count += 1
                logger.info(f"_validate_questions: 过滤题目: {question.get('question', '无题目内容')[:50]}...")
                continue

            filtered_questions.append(question)

        if filtered_count > 0:
            logger.info(f"共过滤掉 {filtered_count} 道包含过滤关键词的题目")

        return {
            'has_valid_questions': len(filtered_questions) > 0,
            'validated_questions': filtered_questions,
            'filtered_count': filtered_count,
            'original_count': len(questions)
        }

    def _should_filter_question(self, question: Dict) -> bool:
        """
        检查题目是否应该被过滤

        Args:
            question: 题目数据

        Returns:
            是否应该过滤
        """
        if not self.config.FILTER_KEYWORDS:
            return False

        # 检查的字段
        fields_to_check = [
            question.get('question', ''),
            question.get('options', ''),
            question.get('explanation', ''),
            question.get('answer', '')
        ]

        # 检查每个字段是否包含过滤关键词
        for field_content in fields_to_check:
            if field_content:
                for keyword in self.config.FILTER_KEYWORDS:
                    if keyword in field_content:
                        logger.debug(f"_should_filter_question: 题目包含过滤关键词 '{keyword}': {field_content[:100]}...")
                        return True

        return False
