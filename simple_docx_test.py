"""
简化的DOCX文档测试脚本
直接读取DOCX文件并测试LLM生成
"""

import os
import sys
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from llm_service.openai_client import OpenAICompatibleClient
from output_manager.ini_writer import INIWriter

def read_docx_simple(file_path):
    """简单读取DOCX文件内容"""
    print("=" * 60)
    print("读取DOCX文件")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"[错误] 文件不存在: {file_path}")
        return None
    
    try:
        doc = Document(file_path)
        
        # 提取所有段落文本
        content_parts = []
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                content_parts.append(text)
        
        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_content = []
                for cell in row.cells:
                    row_content.append(cell.text.strip())
                if any(row_content):  # 如果行不为空
                    content_parts.append(' | '.join(row_content))
        
        content = '\n\n'.join(content_parts)
        
        print(f"[成功] 文档读取成功")
        print(f"[文件] 文件名: {os.path.basename(file_path)}")
        print(f"[统计] 段落数量: {len(doc.paragraphs)}")
        print(f"[统计] 表格数量: {len(doc.tables)}")
        print(f"[统计] 内容长度: {len(content)} 字符")
        print(f"[统计] 词汇数量: {len(content.split())} 个")
        
        return content
        
    except Exception as e:
        print(f"[错误] 读取失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_content_quality(content):
    """分析内容质量"""
    print("\n" + "=" * 60)
    print("内容质量分析")
    print("=" * 60)
    
    if not content:
        print("[错误] 没有内容可供分析")
        return False

    # 基本统计
    char_count = len(content)
    word_count = len(content.split())
    line_count = len(content.split('\n'))
    sentence_count = content.count('。') + content.count('.')

    print(f"[统计] 基本统计:")
    print(f"  字符数: {char_count}")
    print(f"  词汇数: {word_count}")
    print(f"  行数: {line_count}")
    print(f"  句子数: {sentence_count}")

    # 内容类型分析
    print(f"\n[分析] 内容类型分析:")

    # 检查是否包含条款结构
    if '第' in content and ('条' in content or '章' in content):
        print(f"  [OK] 包含条款结构")
    else:
        print(f"  [警告] 缺少明显的条款结构")

    # 检查规范性语言
    regulatory_keywords = ['规定', '要求', '应当', '必须', '禁止', '不得', '应该']
    found_regulatory = [kw for kw in regulatory_keywords if kw in content]
    if found_regulatory:
        print(f"  [OK] 包含规范性语言: {', '.join(found_regulatory[:5])}")
    else:
        print(f"  [警告] 缺少规范性语言")

    # 检查专业术语
    technical_keywords = ['输煤', '机组', '运行', '操作', '设备', '系统', '控制', '安全']
    found_technical = [kw for kw in technical_keywords if kw in content]
    if found_technical:
        print(f"  [OK] 包含专业术语: {', '.join(found_technical[:5])}")
    else:
        print(f"  [警告] 缺少专业术语")

    # 内容充实度评估
    print(f"\n[评估] 内容充实度评估:")
    
    quality_score = 0
    issues = []
    
    if char_count < 500:
        issues.append("内容过短")
    else:
        quality_score += 1
        
    if word_count < 50:
        issues.append("词汇量不足")
    else:
        quality_score += 1
        
    if sentence_count < 5:
        issues.append("句子数量过少")
    else:
        quality_score += 1
        
    if len(found_regulatory) == 0:
        issues.append("缺少规范性语言")
    else:
        quality_score += 1
        
    if len(found_technical) == 0:
        issues.append("缺少专业术语")
    else:
        quality_score += 1
    
    if quality_score >= 4:
        print(f"  [优秀] 内容质量良好 (评分: {quality_score}/5)")
        return True
    elif quality_score >= 2:
        print(f"  [一般] 内容质量一般 (评分: {quality_score}/5)")
        print(f"  问题: {', '.join(issues)}")
        return True
    else:
        print(f"  [较差] 内容质量较差 (评分: {quality_score}/5)")
        print(f"  问题: {', '.join(issues)}")
        return False

def test_llm_generation(content):
    """测试LLM生成"""
    print("\n" + "=" * 60)
    print("LLM题目生成测试")
    print("=" * 60)
    
    if not content:
        print("[错误] 没有内容可供测试")
        return None
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    config.OUTPUT_DIR = "test_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建LLM客户端和INI写入器
    llm_client = OpenAICompatibleClient(config)
    ini_writer = INIWriter(config)
    
    try:
        print("[测试] 开始生成题目...")

        # 生成题目
        quiz_data = llm_client.generate_quiz(
            content=content,
            source_filename="附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
        )

        if quiz_data and 'questions' in quiz_data:
            questions = quiz_data['questions']
            print(f"[成功] 成功生成 {len(questions)} 道题目")

            # 显示生成的题目概览
            for i, question in enumerate(questions, 1):
                print(f"\n[题目] 题目 {i}:")
                print(f"  类型: 【{question.get('type', '未知')}题】")
                print(f"  题目: {question.get('question', '')[:80]}...")
                print(f"  答案: {question.get('answer', 'N/A')}")
                print(f"  难度: {question.get('difficulty', 'N/A')}")

                # 特别检查简答题的关键词格式
                if question.get('type') == '简答':
                    answer = question.get('answer', '')
                    if '；' in answer or '|' in answer:
                        keywords = answer.replace('；', '|').split('|')
                        print(f"  关键词: {len(keywords)} 个")
                        for j, keyword in enumerate(keywords[:3], 1):
                            keyword = keyword.strip()
                            if len(keyword) >= 2:
                                print(f"    关键词{j}: '{keyword}' [OK]")
                            else:
                                print(f"    关键词{j}: '{keyword}' [警告] (过短)")

            # 保存为INI文件
            ini_file = ini_writer.save_questions_to_ini(questions, "输煤运行规程_测试生成.ini")
            print(f"\n[保存] 题库已保存: {ini_file}")

            return questions

        elif quiz_data and quiz_data.get('insufficient_content'):
            print(f"[警告] LLM报告内容不足以生成题目")
            print(f"[原因] 可能的原因:")
            print(f"  1. 文档内容过于简单或重复")
            print(f"  2. 缺少具体的规范性内容")
            print(f"  3. 专业术语不足")
            print(f"  4. 文档结构不清晰")
            return None
        else:
            print(f"[失败] 生成失败，未知原因")
            return None

    except Exception as e:
        print(f"[错误] 生成异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def provide_suggestions(content, questions):
    """提供改进建议"""
    print("\n" + "=" * 60)
    print("改进建议")
    print("=" * 60)
    
    if questions:
        print("[成功] 题目生成成功！建议:")
        print("1. 检查生成的题目质量和准确性")
        print("2. 验证简答题关键词格式是否正确")
        print("3. 确认题目难度分布是否合理")
        print("4. 可以尝试调整题目数量配置")
    else:
        print("[失败] 题目生成失败，建议:")
        print("1. 检查文档内容是否完整")
        print("2. 确认文档是否包含足够的实质性信息")
        print("3. 检查文档格式是否正确")
        print("4. 考虑手动补充更多专业内容")
        
        if content:
            char_count = len(content)
            word_count = len(content.split())
            
            if char_count < 1000:
                print("5. 文档内容过短，建议合并多个相关文档")
            if word_count < 100:
                print("6. 词汇量不足，建议增加更多描述性内容")
            if '规定' not in content and '要求' not in content:
                print("7. 缺少规范性语言，建议添加具体的操作要求")

def main():
    """主函数"""
    print("[测试] 660MW超临界机组输煤运行规程文档测试")
    
    # 文档路径
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    # 步骤1: 读取文档
    content = read_docx_simple(doc_path)
    
    if content:
        # 显示内容预览
        print(f"\n[预览] 内容预览（前1000字符）:")
        print("-" * 40)
        print(content[:1000])
        if len(content) > 1000:
            print("...")
        print("-" * 40)
        
        # 步骤2: 分析内容质量
        quality_ok = analyze_content_quality(content)
        
        # 步骤3: 测试LLM生成
        questions = test_llm_generation(content)
        
        # 步骤4: 提供建议
        provide_suggestions(content, questions)
        
        print(f"\n[完成] 测试完成！")
    else:
        print(f"\n[错误] 无法读取文档，测试终止")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
