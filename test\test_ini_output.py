"""
测试INI格式输出功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from output_manager.ini_writer import INIWriter
from llm_service.openai_client import OpenAICompatibleClient

def test_ini_writer():
    """测试INI写入器"""
    print("=" * 60)
    print("测试INI格式输出")
    print("=" * 60)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建配置
        config = Config()
        config.OUTPUT_DIR = temp_dir
        
        # 创建INI写入器
        ini_writer = INIWriter(config)
        
        # 测试数据
        test_questions = [
            {
                'type': '单选',
                'question': '根据新模板.ini格式要求，单选题最多支持多少个选项？',
                'options': '4个选项（A、B、C、D）|6个选项（A到F）|8个选项（A到H）|12个选项（A到L）',
                'answer': 'D',
                'difficulty': '简单',
                'explanation': '根据《新模板填写规范》第4条规定，单选、多选题选项个数最多支持12个：A B C D E F G H I J K L。',
                'knowledge_points': ['新模板填写规范', '选项数量限制']
            },
            {
                'type': '多选',
                'question': '以下哪些是新模板.ini格式支持的题型？',
                'options': '单选题|多选题|判断题|填空题|简答题|排序题',
                'answer': 'ABCDE',
                'difficulty': '一般',
                'explanation': '依据《新模板使用指南》，支持单选题、多选题、判断题、填空题、简答题这五种题型。',
                'knowledge_points': ['新模板使用指南', '支持题型']
            },
            {
                'type': '判断',
                'question': '新模板.ini格式中，判断题的正确答案应该填写"对"或"错"。',
                'answer': '对',
                'difficulty': '简单',
                'explanation': '按照《新模板标准规范》第5条要求，判断题正确答案填写"对或错"。',
                'knowledge_points': ['新模板标准规范', '判断题答案格式']
            },
            {
                'type': '填空',
                'question': '新模板中，填空题最多支持（）个空，每个空可设置（）个备选答案。',
                'answer': '12|3',
                'difficulty': '一般',
                'explanation': '根据《新模板技术规范》，这些是填空题的标准格式要求。',
                'knowledge_points': ['新模板技术规范', '填空题格式']
            },
            {
                'type': '简答',
                'question': '请简述新模板.ini格式相比传统格式的主要优势。',
                'answer': '标准化程度高；支持题型丰富；操作简单易用；系统兼容性好',
                'difficulty': '较难',
                'explanation': '根据《新模板设计理念》和《用户使用反馈报告》，新模板在这些方面都有显著提升。',
                'knowledge_points': ['新模板设计理念', '用户使用反馈', '格式优势']
            }
        ]
        
        # 保存到INI文件
        ini_file = ini_writer.save_questions_to_ini(test_questions, "test_output.ini")
        print(f"[OK] INI文件已保存到: {ini_file}")

        # 读取并显示生成的内容
        with open(ini_file, 'r', encoding='utf-8') as f:
            content = f.read()

        print("\n生成的INI文件内容:")
        print("-" * 60)
        print(content[:2000])  # 显示前2000个字符
        if len(content) > 2000:
            print("...")
            print(f"（内容总长度: {len(content)} 字符）")

        # 生成摘要报告
        summary = ini_writer.generate_summary_report(test_questions)
        print(f"\n[统计] 题目统计:")
        for key, value in summary.items():
            print(f"  {key}: {value}")

        print(f"\n[OK] 测试完成！文件保存在: {ini_file}")

def test_llm_ini_parsing():
    """测试LLM客户端的INI格式解析"""
    print("\n" + "=" * 60)
    print("测试LLM客户端INI格式解析")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    
    # 创建LLM客户端
    llm_client = OpenAICompatibleClient(config)
    
    # 模拟INI格式响应
    ini_response = """1.【单选题】根据新模板.ini格式要求，单选题最多支持多少个选项？
A、4个选项（A、B、C、D）
B、6个选项（A到F）
C、8个选项（A到H）
D、12个选项（A到L）
正确答案：D
题目难度：简单
答案解析：根据《新模板填写规范》第4条规定，单选、多选题选项个数最多支持12个：A B C D E F G H I J K L。
知识点：新模板填写规范|选项数量限制

2.【判断题】新模板.ini格式中，判断题的正确答案应该填写"对"或"错"。
正确答案：对
题目难度：简单
答案解析：按照《新模板标准规范》第5条要求，判断题正确答案填写"对或错"。
知识点：新模板标准规范|判断题答案格式"""
    
    # 解析响应
    parsed_result = llm_client._parse_ini_response(ini_response)
    
    if parsed_result:
        print("[OK] INI格式解析成功！")
        print(f"解析到 {len(parsed_result['questions'])} 道题目:")

        for i, question in enumerate(parsed_result['questions'], 1):
            print(f"\n题目 {i}:")
            print(f"  类型: {question['type']}")
            print(f"  题目: {question['question'][:50]}...")
            print(f"  答案: {question.get('answer', 'N/A')}")
            print(f"  难度: {question.get('difficulty', 'N/A')}")
            print(f"  知识点: {question.get('knowledge_points', [])}")
    else:
        print("[ERROR] INI格式解析失败")

def main():
    """主测试函数"""
    try:
        test_ini_writer()
        test_llm_ini_parsing()
        print("\n[SUCCESS] 所有测试完成！")
    except Exception as e:
        print(f"[ERROR] 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
