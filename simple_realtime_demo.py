"""
简化的实时错误保存演示
不依赖pdfplumber，直接模拟文档处理流程
"""

import os
import sys
import time
import json
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from error_handler.error_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def create_demo_config():
    """创建演示配置"""
    config_data = {
        "api": {
            "base_url": "http://************:3001",
            "key": "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3",
            "model": "rsv-rcgjfz7v",
            "max_tokens": 4000,
            "temperature": 0.7,
            "timeout": 60,
            "max_retries": 3
        },
        "processing": {
            "max_chunk_size": 1000,  # 故意设置较小，模拟内容不足
            "questions_per_chunk": 4,
            "single_choice_count": 2,
            "multiple_choice_count": 1,
            "fill_blank_count": 0,
            "short_answer_count": 1,
            "true_false_count": 0,
            "sorting_count": 0,
            "disable_document_splitting": False,
            "enable_chunk_merging": True
        },
        "output": {
            "dir": "demo_output",
            "error_dir": "demo_error_docs"
        }
    }
    
    config_file = "demo_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    return Config.from_json_file(config_file)

def read_real_document():
    """读取真实文档内容"""
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    if not os.path.exists(doc_path):
        print(f"[警告] 真实文档不存在，使用模拟内容")
        return create_mock_content()
    
    try:
        doc = Document(doc_path)
        content_parts = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                content_parts.append(text)
        
        content = '\n\n'.join(content_parts)
        print(f"[成功] 读取真实文档，长度: {len(content)} 字符")
        return content
        
    except Exception as e:
        print(f"[错误] 读取真实文档失败: {str(e)}")
        return create_mock_content()

def create_mock_content():
    """创建模拟文档内容"""
    mock_content = """
第一章 总则

第一条 为规范660MW超临界机组输煤系统运行，确保设备安全可靠运行，制定本规程。

第二条 本规程适用于660MW超临界机组输煤系统的运行操作。

第二章 系统概述

第三条 输煤系统主要包括卸煤设备、输送设备、储煤设备等。

第四条 输煤系统应保证连续、稳定、安全的煤炭供应。

第三章 运行要求

第五条 运行人员应熟悉设备性能和操作规程。

第六条 设备启动前应进行全面检查。

第四章 操作规程

第七条 设备启动应按规定顺序进行。

第八条 运行中应密切监视设备状态。

第五章 安全措施

第九条 严禁在运行设备上进行检修作业。

第十条 发现异常应立即停机检查。
"""
    print(f"[模拟] 使用模拟文档内容，长度: {len(mock_content)} 字符")
    return mock_content

def create_document_chunks(content, chunk_size=1000):
    """创建文档分块"""
    chunks = []
    start = 0
    chunk_index = 0
    
    while start < len(content):
        end = start + chunk_size
        
        # 尝试在句号或换行处分割
        if end < len(content):
            for i in range(end, min(end + 100, len(content))):
                if content[i] in '。\n':
                    end = i + 1
                    break
        
        chunk_content = content[start:end].strip()
        
        if chunk_content:
            chunks.append({
                'filename': '附件5：660MW超临界机组输煤运行规程（2024修订）.docx',
                'chunk_index': chunk_index,
                'content': chunk_content,
                'char_count': len(chunk_content),
                'token_count': len(chunk_content.split()),
                'start_pos': start,
                'end_pos': end
            })
            chunk_index += 1
        
        start = end
    
    return chunks

def simulate_llm_processing(chunk):
    """模拟LLM处理，故意让一些块失败"""
    # 模拟不同的失败情况
    char_count = chunk['char_count']
    
    if char_count < 200:
        # 内容太短，模拟LLM报告内容不足
        return None, "insufficient_content"
    elif char_count < 400:
        # 随机失败一些中等长度的块
        import random
        if random.random() < 0.6:  # 60%的概率失败
            return None, "insufficient_content"
        else:
            # 模拟成功生成
            return {
                'questions': [
                    {'type': '单选', 'question': '模拟题目1', 'answer': 'A'},
                    {'type': '简答', 'question': '模拟题目2', 'answer': '关键词1/关键词2'}
                ]
            }, "success"
    else:
        # 长内容通常成功
        return {
            'questions': [
                {'type': '单选', 'question': '模拟题目1', 'answer': 'A'},
                {'type': '多选', 'question': '模拟题目2', 'answer': 'AB'},
                {'type': '简答', 'question': '模拟题目3', 'answer': '关键词1/关键词2/关键词3'}
            ]
        }, "success"

def run_realtime_demo():
    """运行实时错误保存演示"""
    print("=" * 60)
    print("实时错误保存功能演示")
    print("=" * 60)
    
    # 创建配置
    config = create_demo_config()
    print(f"[配置] 分块大小: {config.MAX_CHUNK_SIZE} 字符")
    print(f"[配置] 输出目录: {config.OUTPUT_DIR}")
    print(f"[配置] 错误目录: {config.ERROR_DIR}")
    
    # 创建错误管理器
    error_manager = ErrorManager(config)
    
    # 读取文档内容
    print(f"\n[步骤1] 读取文档内容...")
    content = read_real_document()
    
    # 创建文档分块
    print(f"\n[步骤2] 创建文档分块...")
    chunks = create_document_chunks(content, config.MAX_CHUNK_SIZE)
    print(f"  分割后块数: {len(chunks)}")
    
    # 显示分块信息
    for i, chunk in enumerate(chunks[:3]):
        print(f"    块{i+1}: {chunk['char_count']} 字符")
    
    # 模拟处理每个块
    print(f"\n[步骤3] 模拟LLM处理（演示实时错误保存）...")
    
    successful_count = 0
    failed_count = 0
    realtime_saved_files = []
    
    for i, chunk in enumerate(chunks):
        print(f"\n  处理块 {i+1}/{len(chunks)}:")
        print(f"    字符数: {chunk['char_count']}")
        print(f"    内容预览: {chunk['content'][:80]}...")
        
        # 模拟LLM处理
        result, status = simulate_llm_processing(chunk)
        
        if status == "success" and result:
            questions = result['questions']
            print(f"    [成功] 生成 {len(questions)} 道题目")
            successful_count += 1
            
        elif status == "insufficient_content":
            print(f"    [失败] LLM报告内容不足")
            failed_count += 1
            
            # 实时保存失败片段
            print(f"    [保存] 实时保存失败片段...")
            try:
                saved_file = error_manager.save_failed_chunk_realtime(
                    chunk, 
                    "LLM报告文档内容不足以生成题目"
                )
                
                if saved_file and os.path.exists(saved_file):
                    realtime_saved_files.append(saved_file)
                    print(f"    [成功] 已保存到: {os.path.basename(saved_file)}")
                else:
                    print(f"    [错误] 保存失败")
                    
            except Exception as e:
                print(f"    [异常] 保存异常: {str(e)}")
        
        else:
            print(f"    [失败] 其他错误")
            failed_count += 1
        
        # 短暂延迟，模拟真实处理
        time.sleep(0.1)
    
    # 显示处理结果
    print(f"\n[步骤4] 处理结果统计:")
    print(f"  总块数: {len(chunks)}")
    print(f"  成功处理: {successful_count} 个块")
    print(f"  失败处理: {failed_count} 个块")
    print(f"  实时保存: {len(realtime_saved_files)} 个文件")
    print(f"  成功率: {successful_count/len(chunks)*100:.1f}%")
    
    # 显示实时保存统计
    print(f"\n[步骤5] 实时保存统计:")
    stats = error_manager.get_realtime_save_statistics()
    print(f"  总实时保存次数: {stats['total_realtime_saves']}")
    print(f"  实时保存文件数: {len(stats['realtime_files'])}")
    print(f"  错误文档目录: {stats['err_docs_dir']}")
    
    if stats['latest_save']:
        print(f"  最新保存时间: {stats['latest_save'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示保存的文件列表
    if stats['realtime_files']:
        print(f"\n[文件列表] 实时保存的文件:")
        for i, file_info in enumerate(stats['realtime_files'][:5], 1):
            print(f"  {i}. {file_info['filename']}")
            print(f"     大小: {file_info['size']} 字节")
            print(f"     时间: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 展示一个保存文件的内容
    if realtime_saved_files:
        print(f"\n[步骤6] 展示保存文件内容:")
        first_file = realtime_saved_files[0]
        print(f"  文件: {os.path.basename(first_file)}")
        
        try:
            with open(first_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                print(f"  内容预览（前12行）:")
                for i, line in enumerate(lines[:12], 1):
                    print(f"    {i:2d}: {line}")
                if len(lines) > 12:
                    print(f"    ... (共 {len(lines)} 行)")
        except Exception as e:
            print(f"  [错误] 读取文件失败: {str(e)}")
    
    return len(realtime_saved_files) > 0

def main():
    """主函数"""
    print("[演示] 660MW超临界机组输煤运行规程 - 实时错误保存功能")
    
    # 运行演示
    success = run_realtime_demo()
    
    # 显示功能说明
    print(f"\n" + "=" * 60)
    print("功能说明")
    print("=" * 60)
    
    print("[实时错误保存的特点]:")
    features = [
        "1. 立即保存：一旦检测到LLM报告内容不足，立即保存失败片段",
        "2. 完整信息：保存原文件名、片段索引、字符数、错误信息等",
        "3. 内容完整：保存完整的文档片段内容，便于后续分析",
        "4. 时间精确：文件名包含精确到毫秒的时间戳",
        "5. 统计功能：提供实时保存统计信息",
        "6. 便于调试：可以分析哪些内容容易导致生成失败"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    # 总结
    print(f"\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    
    if success:
        print("[成功] 实时错误保存功能正常工作")
        print("[位置] 错误片段保存在 demo_error_docs/err-docs/ 目录")
        print("[用途] 可用于分析和优化文档处理策略")
    else:
        print("[提示] 本次演示中没有失败片段需要保存")
    
    print(f"\n[下一步建议]:")
    print(f"  1. 查看保存的错误文件，分析失败原因")
    print(f"  2. 根据失败模式调整分块大小")
    print(f"  3. 优化LLM提示词以提高成功率")
    print(f"  4. 考虑对特定类型的内容进行预处理")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 演示程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
