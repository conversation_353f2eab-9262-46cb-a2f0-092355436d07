"""
CSV输出管理模块
负责将生成的题目保存为CSV格式
"""

import os
import csv
import logging
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class CSVWriter:
    """CSV文件写入器"""

    def __init__(self, config):
        """
        初始化CSV写入器

        Args:
            config: 配置对象
        """
        self.config = config
        self.output_dir = config.OUTPUT_DIR
        self.csv_filename = config.CSV_FILENAME

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 固定CSV列结构，不再从模板读取
        self.csv_columns = [
            '试题题干(必填)', '试题类型(必填，题型请用下拉菜单实现）', '选项（用\'|\'隔开）',
            '答案（填空题用\'|\'隔开）(必填)', '分数', '难易度 (必填，难易度请选择下拉菜单实现)',
            '试题解析', '归属目录编码（必填）', '归属部门编码（必填）',
            '知识点' # Added knowledge points
        ]

        logger.info(f"CSV写入器初始化完成，输出目录: {self.output_dir}")

    def save_questions_to_csv(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        保存题目到CSV文件

        Args:
            questions: 题目列表
            filename: 输出文件名，默认使用配置中的文件名

        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = self.csv_filename

        file_path = os.path.join(self.output_dir, filename)

        try:
            # 转换题目数据为CSV格式
            csv_data = self._convert_questions_to_csv_format(questions)

            # 写入CSV文件
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.csv_columns)
                writer.writeheader()
                writer.writerows(csv_data)

            logger.info(f"成功保存 {len(questions)} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
            raise

    def append_questions_to_csv(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        追加题目到现有CSV文件

        Args:
            questions: 题目列表
            filename: 文件名

        Returns:
            文件路径
        """
        if filename is None:
            filename = self.csv_filename

        file_path = os.path.join(self.output_dir, filename)

        try:
            # 转换题目数据
            csv_data = self._convert_questions_to_csv_format(questions)

            # 检查文件是否存在
            file_exists = os.path.exists(file_path)

            with open(file_path, 'a', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.csv_columns)

                # 如果文件不存在，写入表头
                if not file_exists:
                    writer.writeheader()

                writer.writerows(csv_data)

            logger.info(f"成功追加 {len(questions)} 道题目到 {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"追加CSV文件失败: {str(e)}")
            raise

    def _convert_questions_to_csv_format(self, questions: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """
        将题目数据转换为CSV格式

        Args:
            questions: 题目列表

        Returns:
            CSV格式的数据列表
        """
        csv_data = []

        for question in questions:
            try:
                # 根据CSV模板格式转换数据
                csv_row = {}

                # 初始化所有列为空
                for col in self.csv_columns:
                    csv_row[col] = ''

                # 填充数据
                question_text = question.get('question_text', '') # Changed to question_text
                question_type = question.get('question_type', '') # Changed to question_type
                options = question.get('options', {}) # Changed to dictionary
                correct_answer = question.get('correct_answer', '') # Changed to correct_answer
                fill_in_answers = question.get('fill_in_answers', []) # New for fill-in-the-blank
                keywords = question.get('keywords', []) # New for short answer

                difficulty = question.get('difficulty', '一般') # Default difficulty
                analysis = question.get('analysis', '暂无解析') # Default analysis
                knowledge_points = question.get('knowledge_points', []) # New for knowledge points

                # 映射到CSV模板列
                if '试题题干(必填)' in self.csv_columns:
                    csv_row['试题题干(必填)'] = question_text
                if '试题类型(必填，题型请用下拉菜单实现）' in self.csv_columns:
                    csv_row['试题类型(必填，题型请用下拉菜单实现）'] = question_type

                if question_type == "单选题" or question_type == "多选题":
                    formatted_options = []
                    # Sort options by key (A, B, C...) before joining
                    for key in sorted(options.keys()):
                        formatted_options.append(f"{key}、{options[key]}")
                    csv_row['选项（用\'|\'隔开）'] = '|'.join(formatted_options)
                    csv_row['答案（填空题用\'|\'隔开）(必填)'] = correct_answer
                elif question_type == "判断题":
                    csv_row['答案（填空题用\'|\'隔开）(必填)'] = correct_answer
                elif question_type == "填空题":
                    # Flatten list of lists for fill_in_answers and join with '/'
                    # Example: [["a", "b"], ["c"]] -> "a/b|c"
                    formatted_fill_in_answers = []
                    for ans_group in fill_in_answers:
                        formatted_fill_in_answers.append("/".join(ans_group))
                    csv_row['答案（填空题用\'|\'隔开）(必填)'] = '|'.join(formatted_fill_in_answers)
                elif question_type == "简答题":
                    # Flatten list of lists for keywords and join with '/'
                    formatted_keywords = []
                    for kw_group in keywords:
                        formatted_keywords.append("/".join(kw_group))
                    csv_row['答案（填空题用\'|\'隔开）(必填)'] = '|'.join(formatted_keywords)


                if '分数' in self.csv_columns:
                    csv_row['分数'] = '1' # Default score to 1
                if '难易度 (必填，难易度请选择下拉菜单实现)' in self.csv_columns:
                    csv_row['难易度 (必填，难易度请选择下拉菜单实现)'] = difficulty
                if '试题解析' in self.csv_columns:
                    csv_row['试题解析'] = analysis
                if '知识点' in self.csv_columns:
                    csv_row['知识点'] = '|'.join(knowledge_points)
                if '归属目录编码（必填）' in self.csv_columns:
                    csv_row['归属目录编码（必填）'] = '' # Placeholder
                if '归属部门编码（必填）' in self.csv_columns:
                    csv_row['归属部门编码（必填）'] = '' # Placeholder

                csv_data.append(csv_row)

            except Exception as e:
                logger.error(f"转换题目数据失败: {str(e)}")
                continue

        return csv_data

    def create_progress_backup(self, questions: List[Dict[str, Any]], backup_suffix: str = None) -> str:
        """
        创建CSV备份文件

        Args:
            questions: 题目列表
            backup_suffix: 备份文件后缀

        Returns:
            备份文件路径
        """
        if not questions:
            logger.info("没有题目可供备份，跳过备份创建。")
            return ""

        if backup_suffix is None:
            backup_suffix = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 使用原始文件名和后缀创建备份文件名
        original_name, original_ext = os.path.splitext(self.csv_filename)
        backup_filename = f"{original_name}_{backup_suffix}{original_ext}"
        backup_file_path = os.path.join(self.output_dir, backup_filename)

        try:
            csv_data = self._convert_questions_to_csv_format(questions)
            with open(backup_file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=self.csv_columns)
                writer.writeheader()
                writer.writerows(csv_data)
            logger.info(f"成功创建CSV备份文件: {backup_file_path}")
            return backup_file_path
        except Exception as e:
            logger.error(f"创建CSV备份文件失败: {str(e)}")
            raise

    def generate_summary_report(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成处理摘要报告

        Args:
            questions: 已处理的题目列表

        Returns:
            包含各类题目数量的字典
        """
        summary = {
            "total_questions": len(questions),
            "单选题": 0,
            "多选题": 0,
            "判断题": 0,
            "填空题": 0,
            "简答题": 0
        }

        for q in questions:
            q_type = q.get('question_type')
            if q_type in summary:
                summary[q_type] += 1

        return summary

    def save_summary_report(self, summary: Dict[str, Any], filename: str = None) -> str:
        """
        保存摘要报告到JSON文件

        Args:
            summary: 摘要报告数据
            filename: 输出文件名，默认使用配置中的文件名

        Returns:
            保存的文件路径
        """
        if filename is None:
            filename = "summary_report.json"

        file_path = os.path.join(self.output_dir, filename)

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=4)
            logger.info(f"成功保存摘要报告到 {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"保存摘要报告失败: {str(e)}")
            raise

    def merge_csv_files(self, csv_files: List[str], output_filename: str = None) -> str:
        """
        合并多个CSV文件到一个文件

        Args:
            csv_files: 要合并的CSV文件路径列表
            output_filename: 合并后的输出文件名，默认使用配置中的文件名

        Returns:
            合并后的文件路径
        """
        if not csv_files:
            logger.warning("没有指定CSV文件进行合并。")
            return ""

        if output_filename is None:
            output_filename = self.csv_filename

        output_file_path = os.path.join(self.output_dir, output_filename)

        all_data = []
        for i, file_path in enumerate(csv_files):
            try:
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                all_data.append(df)
            except Exception as e:
                logger.error(f"读取文件 {file_path} 失败，跳过: {str(e)}")
                continue

        if not all_data:
            logger.warning("所有CSV文件读取失败，未进行合并。")
            return ""

        try:
            merged_df = pd.concat(all_data, ignore_index=True)
            merged_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            logger.info(f"成功合并 {len(csv_files)} 个CSV文件到 {output_file_path}")
            return output_file_path
        except Exception as e:
            logger.error(f"合并CSV文件失败: {str(e)}")
            raise
