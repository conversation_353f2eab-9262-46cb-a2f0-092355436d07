"""
使用系统完全相同的提示词测试失败片段
验证是否是题目数量要求过高导致的失败
"""

import os
import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config

def read_failed_chunk():
    """读取失败片段内容"""
    file_path = "err-docs/failed_realtime_附件5：660MW超临界机组输煤运行规程（2024修订）.docx_chunk5_20250619_151730_475.txt"
    
    if not os.path.exists(file_path):
        print(f"[错误] 文件不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取文档内容部分
        lines = content.split('\n')
        content_start = False
        document_content = []
        
        for line in lines:
            if line.strip() == "=== 文档内容 ===":
                content_start = True
                continue
            if content_start:
                document_content.append(line)
        
        return '\n'.join(document_content)
        
    except Exception as e:
        print(f"[错误] 读取文件失败: {str(e)}")
        return None

def test_with_system_prompt(content):
    """使用系统完全相同的提示词测试"""
    print("=" * 60)
    print("使用系统提示词测试")
    print("=" * 60)
    
    # 加载系统配置
    config = Config.from_json_file("test_config.json")
    
    # 构建完全相同的提示词
    system_prompt = config.QUIZ_PROMPT_TEMPLATE.format(
        single_choice_count=config.SINGLE_CHOICE_COUNT,
        multiple_choice_count=config.MULTIPLE_CHOICE_COUNT,
        fill_blank_count=config.FILL_BLANK_COUNT,
        short_answer_count=config.SHORT_ANSWER_COUNT,
        true_false_count=config.TRUE_FALSE_COUNT,
        source_filename="附件5：660MW超临界机组输煤运行规程（2024修订）.docx",
        content=content
    )
    
    print(f"[配置] 系统要求的题目数量:")
    print(f"  单选题: {config.SINGLE_CHOICE_COUNT}道")
    print(f"  多选题: {config.MULTIPLE_CHOICE_COUNT}道")
    print(f"  填空题: {config.FILL_BLANK_COUNT}道")
    print(f"  简答题: {config.SHORT_ANSWER_COUNT}道")
    print(f"  判断题: {config.TRUE_FALSE_COUNT}道")
    print(f"  总计: {config.SINGLE_CHOICE_COUNT + config.MULTIPLE_CHOICE_COUNT + config.FILL_BLANK_COUNT + config.SHORT_ANSWER_COUNT + config.TRUE_FALSE_COUNT}道")
    
    # API配置
    api_url = f"{config.API_BASE_URL}/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {config.API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": config.MODEL_NAME,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的题目生成助手。请严格按照要求的格式输出，确保简答题的关键词是核心概念而不是单个字。"
            },
            {
                "role": "user",
                "content": system_prompt
            }
        ],
        "max_tokens": config.MAX_TOKENS,
        "temperature": config.TEMPERATURE
    }
    
    print(f"\n[测试] 使用系统完全相同的提示词...")
    print(f"  提示词长度: {len(system_prompt)} 字符")
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=config.REQUEST_TIMEOUT)
        
        if response.status_code == 200:
            response_json = response.json()
            content_response = response_json['choices'][0]['message']['content']
            
            print(f"  API响应长度: {len(content_response)} 字符")
            
            if "insufficient_content" in content_response.lower():
                print(f"  [失败] LLM报告内容不足")
                print(f"  响应: {content_response[:300]}...")
                return False
            elif "【单选题】" in content_response or "【多选题】" in content_response:
                print(f"  [成功] LLM生成了题目")
                
                # 统计生成的题目数量
                single_count = content_response.count("【单选题】")
                multiple_count = content_response.count("【多选题】")
                fill_count = content_response.count("【填空题】")
                short_count = content_response.count("【简答题】")
                true_false_count = content_response.count("【判断题】")
                
                print(f"  生成统计:")
                print(f"    单选题: {single_count}道")
                print(f"    多选题: {multiple_count}道")
                print(f"    填空题: {fill_count}道")
                print(f"    简答题: {short_count}道")
                print(f"    判断题: {true_false_count}道")
                print(f"    总计: {single_count + multiple_count + fill_count + short_count + true_false_count}道")
                
                print(f"\n  响应预览:")
                print("  " + "-" * 40)
                for i, line in enumerate(content_response.split('\n')[:20], 1):
                    print(f"  {i:2d}: {line}")
                print("  " + "-" * 40)
                
                return True
            else:
                print(f"  [异常] LLM响应格式异常")
                print(f"  响应预览: {content_response[:300]}...")
                return False
        else:
            print(f"  [错误] API调用失败: {response.status_code}")
            print(f"  响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"  [异常] API调用异常: {str(e)}")
        return False

def test_with_reduced_requirements(content):
    """测试减少题目数量要求"""
    print("\n" + "=" * 60)
    print("测试减少题目数量要求")
    print("=" * 60)
    
    # 加载系统配置
    config = Config.from_json_file("test_config.json")
    
    # 测试不同的题目数量组合
    test_cases = [
        {
            "name": "减少到4道题目",
            "single_choice_count": 2,
            "multiple_choice_count": 1,
            "fill_blank_count": 0,
            "short_answer_count": 1,
            "true_false_count": 0
        },
        {
            "name": "减少到3道题目",
            "single_choice_count": 2,
            "multiple_choice_count": 0,
            "fill_blank_count": 0,
            "short_answer_count": 1,
            "true_false_count": 0
        },
        {
            "name": "只要求2道单选题",
            "single_choice_count": 2,
            "multiple_choice_count": 0,
            "fill_blank_count": 0,
            "short_answer_count": 0,
            "true_false_count": 0
        }
    ]
    
    api_url = f"{config.API_BASE_URL}/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {config.API_KEY}",
        "Content-Type": "application/json"
    }
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[测试 {i}] {test_case['name']}:")
        
        # 构建提示词
        test_prompt = config.QUIZ_PROMPT_TEMPLATE.format(
            single_choice_count=test_case['single_choice_count'],
            multiple_choice_count=test_case['multiple_choice_count'],
            fill_blank_count=test_case['fill_blank_count'],
            short_answer_count=test_case['short_answer_count'],
            true_false_count=test_case['true_false_count'],
            source_filename="附件5：660MW超临界机组输煤运行规程（2024修订）.docx",
            content=content
        )
        
        total_questions = (test_case['single_choice_count'] + test_case['multiple_choice_count'] + 
                          test_case['fill_blank_count'] + test_case['short_answer_count'] + 
                          test_case['true_false_count'])
        
        print(f"  要求题目总数: {total_questions}道")
        
        data = {
            "model": config.MODEL_NAME,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的题目生成助手。请严格按照要求的格式输出。"
                },
                {
                    "role": "user",
                    "content": test_prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        try:
            response = requests.post(api_url, headers=headers, json=data, timeout=config.REQUEST_TIMEOUT)
            
            if response.status_code == 200:
                response_json = response.json()
                content_response = response_json['choices'][0]['message']['content']
                
                if "insufficient_content" in content_response.lower():
                    print(f"  [失败] LLM报告内容不足")
                elif "【单选题】" in content_response or "【多选题】" in content_response:
                    print(f"  [成功] LLM生成了题目")
                    
                    # 统计生成的题目数量
                    single_count = content_response.count("【单选题】")
                    multiple_count = content_response.count("【多选题】")
                    fill_count = content_response.count("【填空题】")
                    short_count = content_response.count("【简答题】")
                    true_false_count = content_response.count("【判断题】")
                    actual_total = single_count + multiple_count + fill_count + short_count + true_false_count
                    
                    print(f"    实际生成: {actual_total}道题目")
                    if actual_total >= total_questions:
                        print(f"    [完成] 满足要求")
                    else:
                        print(f"    [部分] 未完全满足要求")
                else:
                    print(f"  [异常] 响应格式异常")
            else:
                print(f"  [错误] API调用失败: {response.status_code}")
                
        except Exception as e:
            print(f"  [异常] API调用异常: {str(e)}")

def main():
    """主函数"""
    print("[验证] 失败片段 - 系统提示词测试")
    print("[目的] 验证是否是题目数量要求过高导致的失败")
    
    # 读取失败片段
    content = read_failed_chunk()
    
    if content:
        print(f"\n[信息] 失败片段基本信息:")
        print(f"  字符数: {len(content)}")
        print(f"  词汇数: {len(content.split())}")
        print(f"  句子数: {content.count('。') + content.count('.')}")
        
        # 测试1: 使用系统完全相同的提示词
        system_success = test_with_system_prompt(content)
        
        # 测试2: 测试减少题目数量要求
        test_with_reduced_requirements(content)
        
        # 总结
        print(f"\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        if system_success:
            print("[结论] 使用系统提示词可以成功生成题目")
            print("[说明] 失败可能是由于其他因素（网络、API状态等）")
        else:
            print("[结论] 使用系统提示词确实失败")
            print("[原因] 可能是题目数量要求过高（6道题目）")
            print("[建议] 减少题目数量要求或优化分块策略")
        
        print(f"\n[关键发现]:")
        print("1. 系统要求生成6道不同类型的题目")
        print("2. 包含填空题和判断题，增加了生成难度")
        print("3. 提示词非常详细和严格，可能限制了LLM的灵活性")
        print("4. 内容虽然丰富，但可能不足以支撑6道高质量题目")
        
        print(f"\n[优化建议]:")
        print("1. 减少每个分块要求的题目数量（从6道减少到3-4道）")
        print("2. 优先生成单选题和简答题，减少填空题和判断题")
        print("3. 根据内容长度动态调整题目数量")
        print("4. 考虑将复杂内容按主题进一步分割")
        
    else:
        print("[错误] 无法读取失败片段内容")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
