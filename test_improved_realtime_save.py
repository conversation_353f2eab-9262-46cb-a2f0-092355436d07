"""
测试改进后的实时错误保存功能
只有在真正无法合并且无法生成题目时才保存错误
"""

import os
import sys
import time
import json
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from error_handler.error_manager import <PERSON><PERSON><PERSON><PERSON>ana<PERSON>

def create_test_config():
    """创建测试配置"""
    config_data = {
        "api": {
            "base_url": "http://************:3001",
            "key": "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3",
            "model": "rsv-rcgjfz7v",
            "max_tokens": 4000,
            "temperature": 0.7,
            "timeout": 60,
            "max_retries": 3
        },
        "processing": {
            "max_chunk_size": 2000,  # 设置较小的分块大小来测试合并逻辑
            "questions_per_chunk": 4,
            "single_choice_count": 2,
            "multiple_choice_count": 1,
            "fill_blank_count": 0,
            "short_answer_count": 1,
            "true_false_count": 0,
            "sorting_count": 0,
            "disable_document_splitting": False,
            "enable_chunk_merging": True  # 启用合并
        },
        "output": {
            "dir": "test_improved_output",
            "error_dir": "test_improved_error_docs"
        }
    }
    
    config_file = "test_improved_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    return Config.from_json_file(config_file)

def read_real_document():
    """读取真实文档内容"""
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    if not os.path.exists(doc_path):
        print(f"[警告] 真实文档不存在，使用模拟内容")
        return create_mock_content()
    
    try:
        doc = Document(doc_path)
        content_parts = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                content_parts.append(text)
        
        content = '\n\n'.join(content_parts)
        print(f"[成功] 读取真实文档，长度: {len(content)} 字符")
        return content
        
    except Exception as e:
        print(f"[错误] 读取真实文档失败: {str(e)}")
        return create_mock_content()

def create_mock_content():
    """创建模拟文档内容"""
    mock_content = """
第一章 总则

第一条 为规范660MW超临界机组输煤系统运行，确保设备安全可靠运行，制定本规程。

第二条 本规程适用于660MW超临界机组输煤系统的运行操作。

第二章 系统概述

第三条 输煤系统主要包括卸煤设备、输送设备、储煤设备等。

第四条 输煤系统应保证连续、稳定、安全的煤炭供应。

第三章 运行要求

第五条 运行人员应熟悉设备性能和操作规程。

第六条 设备启动前应进行全面检查。

第四章 操作规程

第七条 设备启动应按规定顺序进行。

第八条 运行中应密切监视设备状态。

第五章 安全措施

第九条 严禁在运行设备上进行检修作业。

第十条 发现异常应立即停机检查。

第六章 维护保养

第十一条 定期检查设备运行状态。

第十二条 及时更换磨损部件。

第七章 故障处理

第十三条 发现故障应立即停机。

第十四条 查明故障原因后方可重新启动。

第八章 记录管理

第十五条 详细记录设备运行参数。

第十六条 建立完整的维护档案。

第九章 培训要求

第十七条 操作人员必须经过专业培训。

第十八条 定期进行技能考核。

第十章 附则

第十九条 本规程自发布之日起执行。

第二十条 本规程由设备部负责解释。
"""
    print(f"[模拟] 使用模拟文档内容，长度: {len(mock_content)} 字符")
    return mock_content

def create_document_chunks(content, chunk_size=2000):
    """创建文档分块"""
    chunks = []
    start = 0
    chunk_index = 0
    
    while start < len(content):
        end = start + chunk_size
        
        # 尝试在句号或换行处分割
        if end < len(content):
            for i in range(end, min(end + 100, len(content))):
                if content[i] in '。\n':
                    end = i + 1
                    break
        
        chunk_content = content[start:end].strip()
        
        if chunk_content:
            chunks.append({
                'filename': '附件5：660MW超临界机组输煤运行规程（2024修订）.docx',
                'chunk_index': chunk_index,
                'content': chunk_content,
                'char_count': len(chunk_content),
                'token_count': len(chunk_content.split()),
                'start_pos': start,
                'end_pos': end
            })
            chunk_index += 1
        
        start = end
    
    return chunks

def simulate_llm_processing_with_merge_logic(chunk, can_merge=True):
    """模拟LLM处理，包含合并逻辑"""
    char_count = chunk['char_count']
    
    # 第一次尝试：模拟内容不足
    if char_count < 1500:
        return None, "insufficient_content", can_merge
    else:
        # 内容足够，成功生成
        return {
            'questions': [
                {'type': '单选', 'question': f'关于{chunk["filename"]}的题目', 'answer': 'A'},
                {'type': '简答', 'question': '简答题目', 'answer': '关键词1/关键词2'}
            ]
        }, "success", False

def simulate_merge_attempt(chunks, current_index, max_chunk_size=5000):
    """模拟合并尝试"""
    if current_index >= len(chunks):
        return None, False, "index_out_of_range"
    
    current_chunk = chunks[current_index]
    current_char_count = current_chunk['char_count']
    
    # 如果当前分块已经足够大，不进行合并
    if current_char_count >= max_chunk_size:
        return None, False, "exceeds_max_chunk_size"
    
    # 尝试与下一个分块合并
    if current_index + 1 < len(chunks):
        next_chunk = chunks[current_index + 1]
        merged_content = current_chunk['content'] + '\n\n' + next_chunk['content']
        merged_char_count = len(merged_content)
        
        if merged_char_count <= max_chunk_size:
            # 合并成功
            merged_chunk = {
                'filename': current_chunk['filename'],
                'chunk_index': current_chunk['chunk_index'],
                'content': merged_content,
                'char_count': merged_char_count,
                'token_count': len(merged_content.split()),
                'merged_from': [current_index, current_index + 1]
            }
            return merged_chunk, True, "merge_success"
    
    return None, False, "no_chunks_to_merge"

def test_improved_realtime_save():
    """测试改进后的实时保存逻辑"""
    print("=" * 60)
    print("测试改进后的实时错误保存功能")
    print("=" * 60)
    
    # 创建配置
    config = create_test_config()
    print(f"[配置] 分块大小: {config.MAX_CHUNK_SIZE} 字符")
    print(f"[配置] 启用合并: {config.ENABLE_CHUNK_MERGING}")
    print(f"[配置] 输出目录: {config.OUTPUT_DIR}")
    print(f"[配置] 错误目录: {config.ERROR_DIR}")
    
    # 创建错误管理器
    error_manager = ErrorManager(config)
    
    # 读取文档内容
    print(f"\n[步骤1] 读取文档内容...")
    content = read_real_document()
    
    # 创建文档分块
    print(f"\n[步骤2] 创建文档分块...")
    chunks = create_document_chunks(content, config.MAX_CHUNK_SIZE)
    print(f"  分割后块数: {len(chunks)}")
    
    # 显示分块信息
    for i, chunk in enumerate(chunks[:3]):
        print(f"    块{i+1}: {chunk['char_count']} 字符")
    
    # 模拟处理每个块
    print(f"\n[步骤3] 模拟改进后的处理逻辑...")
    
    successful_count = 0
    failed_count = 0
    realtime_saved_files = []
    merge_attempts = 0
    merge_successes = 0
    
    i = 0
    while i < len(chunks):
        chunk = chunks[i]
        print(f"\n  处理块 {i+1}/{len(chunks)}:")
        print(f"    字符数: {chunk['char_count']}")
        print(f"    内容预览: {chunk['content'][:80]}...")
        
        # 第一次尝试LLM处理
        result, status, can_merge = simulate_llm_processing_with_merge_logic(chunk)
        
        if status == "success":
            print(f"    [成功] 直接生成题目成功")
            successful_count += 1
            i += 1
            
        elif status == "insufficient_content" and can_merge and config.ENABLE_CHUNK_MERGING:
            print(f"    [尝试] 内容不足，尝试合并...")
            merge_attempts += 1
            
            # 尝试合并
            merged_chunk, merge_success, merge_reason = simulate_merge_attempt(chunks, i, config.MAX_CHUNK_SIZE)
            
            if merge_success:
                print(f"    [合并] 合并成功，新字符数: {merged_chunk['char_count']}")
                merge_successes += 1
                
                # 对合并后的块再次尝试LLM处理
                merged_result, merged_status, _ = simulate_llm_processing_with_merge_logic(merged_chunk, False)
                
                if merged_status == "success":
                    print(f"    [成功] 合并后生成题目成功")
                    successful_count += 1
                    i += 2  # 跳过已合并的两个块
                else:
                    print(f"    [失败] 合并后仍无法生成题目")
                    failed_count += 1
                    
                    # 只有在合并后仍无法生成题目时才实时保存
                    try:
                        saved_file = error_manager.save_failed_chunk_realtime(
                            merged_chunk, 
                            f"合并后仍无法生成题目，LLM报告内容不足，字符数: {merged_chunk['char_count']}"
                        )
                        if saved_file:
                            realtime_saved_files.append(saved_file)
                            print(f"    [保存] 实时保存失败片段: {os.path.basename(saved_file)}")
                    except Exception as e:
                        print(f"    [错误] 保存异常: {str(e)}")
                    
                    i += 2  # 跳过已合并的两个块
            else:
                print(f"    [失败] 无法合并: {merge_reason}")
                failed_count += 1
                
                # 只有在真正无法合并时才实时保存
                try:
                    saved_file = error_manager.save_failed_chunk_realtime(
                        chunk, 
                        f"LLM报告内容不足且无法有效合并，字符数: {chunk['char_count']}"
                    )
                    if saved_file:
                        realtime_saved_files.append(saved_file)
                        print(f"    [保存] 实时保存失败片段: {os.path.basename(saved_file)}")
                except Exception as e:
                    print(f"    [错误] 保存异常: {str(e)}")
                
                i += 1
        else:
            print(f"    [失败] 内容不足且无法合并")
            failed_count += 1
            
            # 无法合并时才实时保存
            try:
                saved_file = error_manager.save_failed_chunk_realtime(
                    chunk, 
                    "LLM报告内容不足且合并已禁用"
                )
                if saved_file:
                    realtime_saved_files.append(saved_file)
                    print(f"    [保存] 实时保存失败片段: {os.path.basename(saved_file)}")
            except Exception as e:
                print(f"    [错误] 保存异常: {str(e)}")
            
            i += 1
        
        # 短暂延迟
        time.sleep(0.05)
    
    # 显示处理结果
    print(f"\n[步骤4] 处理结果统计:")
    print(f"  总块数: {len(chunks)}")
    print(f"  成功处理: {successful_count} 个块")
    print(f"  失败处理: {failed_count} 个块")
    print(f"  合并尝试: {merge_attempts} 次")
    print(f"  合并成功: {merge_successes} 次")
    print(f"  实时保存: {len(realtime_saved_files)} 个文件")
    print(f"  成功率: {successful_count/len(chunks)*100:.1f}%")
    
    # 显示实时保存统计
    print(f"\n[步骤5] 实时保存统计:")
    stats = error_manager.get_realtime_save_statistics()
    print(f"  总实时保存次数: {stats['total_realtime_saves']}")
    print(f"  实时保存文件数: {len(stats['realtime_files'])}")
    
    if stats['realtime_files']:
        print(f"\n[文件列表] 实时保存的文件:")
        for i, file_info in enumerate(stats['realtime_files'][:3], 1):
            print(f"  {i}. {file_info['filename']}")
            print(f"     大小: {file_info['size']} 字节")
            print(f"     时间: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    return len(realtime_saved_files) > 0

def main():
    """主函数"""
    print("[测试] 改进后的实时错误保存功能")
    print("[说明] 只有在真正无法合并且无法生成题目时才保存错误")
    
    # 运行测试
    has_errors = test_improved_realtime_save()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    print("[改进后的逻辑]:")
    improvements = [
        "1. 第一次LLM返回None时，不立即保存错误",
        "2. 先尝试合并分块，增加内容长度",
        "3. 只有合并后仍无法生成题目时，才保存错误",
        "4. 如果无法合并（达到最大分块大小），才保存错误",
        "5. 减少了不必要的错误保存，提高了处理效率"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    if has_errors:
        print(f"\n[结果] 测试中发现了真正的失败片段")
        print(f"[说明] 这些片段即使合并后也无法生成题目")
        print(f"[位置] 错误片段保存在 test_improved_error_docs/err-docs/ 目录")
    else:
        print(f"\n[结果] 测试中没有真正的失败片段")
        print(f"[说明] 所有内容不足的片段都通过合并成功处理")
    
    print(f"\n[优势] 改进后的实时保存功能:")
    print(f"  - 减少误报：不会保存可以通过合并解决的片段")
    print(f"  - 精确定位：只保存真正无法处理的片段")
    print(f"  - 提高效率：减少不必要的文件I/O操作")
    print(f"  - 便于分析：保存的都是真正需要关注的问题片段")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
