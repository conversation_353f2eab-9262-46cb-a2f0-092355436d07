"""
错误处理管理模块
处理失败的文档片段，保存错误信息
"""

import os
import logging
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class ErrorManager:
    """错误管理器"""

    def __init__(self, config):
        """
        初始化错误管理器

        Args:
            config: 配置对象
        """
        self.config = config
        self.error_dir = config.ERROR_DIR
        self.err_docs_dir = os.path.join(os.path.dirname(self.error_dir), "err-docs")

        # 确保错误目录存在
        os.makedirs(self.error_dir, exist_ok=True)
        os.makedirs(self.err_docs_dir, exist_ok=True)

        # 错误日志文件
        self.error_log_file = os.path.join(self.error_dir, "error_log.txt")

    def save_failed_chunk(self, chunk: Dict[str, Any], error_message: str = "", is_successful_for_this_chunk: bool = False, has_generated_questions: bool = False) -> str:
        """
        保存失败的文档片段

        Args:
            chunk: 失败的文档片段
            error_message: 错误信息
            is_successful_for_this_chunk: 指示该文本块是否成功生成了题目。如果为True，则不记录错误日志。
            has_generated_questions: 指示是否已经有题目生成。如果为True，则不保存文件和记录错误日志。

        Returns:
            保存的文件路径
        """
        try:
            # 无论是否已有题目生成，都保存失败片段到 err-docs
            filename = chunk.get('filename', '未知文件')
            chunk_index = chunk.get('chunk_index', 0)
            char_count = len(chunk.get('content', '')) if chunk.get('content') else 0
            
            logger.info(f"准备保存失败片段 - 文件: {filename}, 分块: {chunk_index+1}, 字数: {char_count}")
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 清理文件名中的特殊字符
            safe_filename = re.sub(r'[\\/*?:"<>|]', "_", chunk.get('filename', 'unknown'))
            filename = f"failed_{safe_filename}_{chunk.get('chunk_index', 0)}_{timestamp}.txt"
            file_path = os.path.join(self.err_docs_dir, filename)
            logger.debug(f"完整保存路径: {file_path}")

            # 保存文档片段内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("=== 失败的文档片段 ===\n")
                f.write(f"原文件名: {chunk.get('filename', 'unknown')}\n")
                f.write(f"片段索引: {chunk.get('chunk_index', 0)}\n")
                f.write(f"Token数量: {chunk.get('token_count', 0)}\n")
                f.write(f"失败时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"错误信息: {error_message}\n")
                f.write("\n=== 文档内容 ===\n")
                f.write(chunk.get('content', ''))

                # 如果有结构信息，也保存
                if 'structure_info' in chunk and chunk['structure_info']:
                    f.write("\n\n=== 结构信息 ===\n")
                    for item in chunk['structure_info']:
                        f.write(f"类型: {item.get('type', 'unknown')}\n")
                        if 'style' in item:
                            f.write(f"样式: {item['style']}\n")
                        f.write("---\n")

            # 记录到错误日志，如果该文本块没有成功生成题目
            if not is_successful_for_this_chunk:
                self._log_error(chunk, error_message, file_path)

            logger.info(f"失败片段已保存到: {file_path}")
            return file_path

        except Exception as e:
            logger.error(f"保存失败片段时出错: {str(e)}")
            raise

    def save_failed_chunks_batch(self, failed_chunks: List[Dict[str, Any]],
                                error_messages: List[str] = None, successful_chunk_indices: List[int] = None, has_generated_questions: bool = False) -> List[str]:
        """
        批量保存失败的文档片段

        Args:
            failed_chunks: 失败的文档片段列表
            error_messages: 对应的错误信息列表
            successful_chunk_indices: 成功生成题目的文本块索引列表。这些文本块即使被标记为失败，其错误也不应被记录。
            has_generated_questions: 指示是否已经有题目生成。如果为True，则不保存文件和记录错误日志。

        Returns:
            保存的文件路径列表
        """
        if error_messages is None:
            error_messages = [""] * len(failed_chunks)
        if successful_chunk_indices is None:
            successful_chunk_indices = []

        saved_files = []

        for i, chunk in enumerate(failed_chunks):
            try:
                error_msg = error_messages[i] if i < len(error_messages) else ""
                is_successful = i in successful_chunk_indices # 判断当前失败的文本块是否属于成功生成的列表
                file_path = self.save_failed_chunk(chunk, error_msg, is_successful_for_this_chunk=is_successful, has_generated_questions=has_generated_questions)
                if file_path:  # 只有实际保存了文件才添加到列表中
                    saved_files.append(file_path)
            except Exception as e:
                logger.error(f"批量保存失败片段时出错 (索引 {i}): {str(e)}")
                continue

        logger.info(f"批量保存完成，共保存 {len(saved_files)} 个失败片段")
        return saved_files

    def _log_error(self, chunk: Dict[str, Any], error_message: str, saved_file: str):
        """
        记录错误到日志文件

        Args:
            chunk: 文档片段
            error_message: 错误信息
            saved_file: 保存的文件路径
        """
        try:
            with open(self.error_log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}]\n")
                f.write(f"文件: {chunk.get('filename', 'unknown')}\n")
                f.write(f"片段: {chunk.get('chunk_index', 0)}\n")
                f.write(f"错误: {error_message}\n")
                f.write(f"保存位置: {saved_file}\n")
                f.write("-" * 50 + "\n")
        except Exception as e:
            logger.error(f"写入错误日志失败: {str(e)}")

    def create_error_summary(self, failed_chunks: List[Dict[str, Any]]) -> str:
        """
        创建错误摘要报告

        Args:
            failed_chunks: 失败的文档片段列表

        Returns:
            摘要报告文件路径
        """
        if not failed_chunks:
            return ""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(self.error_dir, f"error_summary_{timestamp}.txt")

        try:
            # 统计错误信息
            file_errors = {}
            total_failed = len(failed_chunks)

            for chunk in failed_chunks:
                filename = chunk.get('filename', 'unknown')
                if filename not in file_errors:
                    file_errors[filename] = 0
                file_errors[filename] += 1

            # 写入摘要报告
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("=== 错误处理摘要报告 ===\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总失败片段数: {total_failed}\n")
                f.write(f"涉及文件数: {len(file_errors)}\n\n")

                f.write("各文件失败统计:\n")
                for filename, count in file_errors.items():
                    f.write(f"  - {filename}: {count} 个片段\n")

                f.write("\n失败片段详情:\n")
                for i, chunk in enumerate(failed_chunks):
                    f.write(f"\n{i+1}. 文件: {chunk.get('filename', 'unknown')}\n")
                    f.write(f"   片段索引: {chunk.get('chunk_index', 0)}\n")
                    f.write(f"   Token数量: {chunk.get('token_count', 0)}\n")
                    f.write(f"   内容预览: {chunk.get('content', '')[:100]}...\n")

            logger.info(f"错误摘要报告已保存到: {summary_file}")
            return summary_file

        except Exception as e:
            logger.error(f"创建错误摘要失败: {str(e)}")
            raise

    def get_error_statistics(self) -> Dict[str, Any]:
        """
        获取错误统计信息

        Returns:
            错误统计数据
        """
        try:
            error_files = []

            if os.path.exists(self.error_dir):
                for filename in os.listdir(self.error_dir):
                    if filename.startswith('failed_chunk_') and filename.endswith('.txt'):
                        file_path = os.path.join(self.error_dir, filename)
                        error_files.append({
                            'filename': filename,
                            'path': file_path,
                            'size': os.path.getsize(file_path),
                            'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                        })

            return {
                'total_error_files': len(error_files),
                'error_files': error_files,
                'error_dir': self.error_dir,
                'has_error_log': os.path.exists(self.error_log_file)
            }

        except Exception as e:
            logger.error(f"获取错误统计失败: {str(e)}")
            return {}

    def cleanup_old_errors(self, days_to_keep: int = 30):
        """
        清理旧的错误文件

        Args:
            days_to_keep: 保留的天数
        """
        try:
            if not os.path.exists(self.error_dir):
                return

            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
            deleted_count = 0

            for filename in os.listdir(self.error_dir):
                file_path = os.path.join(self.error_dir, filename)

                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)

                    if file_time < cutoff_time:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除旧错误文件: {filename}")

            logger.info(f"清理完成，删除了 {deleted_count} 个旧错误文件")

        except Exception as e:
            logger.error(f"清理旧错误文件失败: {str(e)}")

    def retry_failed_chunks(self, failed_chunks: List[Dict[str, Any]],
                           llm_client, max_retries: int = 2) -> tuple:
        """
        重试处理失败的文档片段

        Args:
            failed_chunks: 失败的文档片段列表
            llm_client: LLM客户端
            max_retries: 最大重试次数

        Returns:
            (成功的题目列表, 仍然失败的片段列表)
        """
        successful_questions = []
        still_failed = []

        for chunk in failed_chunks:
            retry_count = 0
            success = False

            while retry_count < max_retries and not success:
                try:
                    logger.info(f"重试处理片段: {chunk.get('filename', 'unknown')} "
                               f"索引 {chunk.get('chunk_index', 0)} (尝试 {retry_count + 1}/{max_retries})")

                    quiz_data = llm_client.generate_quiz(
                        content=chunk['content'],
                        source_filename=chunk.get('filename', '未知文档')
                    )

                    if quiz_data and 'questions' in quiz_data:
                        # 为每个题目添加来源信息
                        for question in quiz_data['questions']:
                            question['source_file'] = chunk['filename']
                            question['chunk_index'] = chunk['chunk_index']
                            successful_questions.append(question)

                        success = True
                        logger.info(f"重试成功，生成 {len(quiz_data['questions'])} 道题目")
                    else:
                        retry_count += 1

                except Exception as e:
                    logger.error(f"重试失败: {str(e)}")
                    retry_count += 1

            if not success:
                still_failed.append(chunk)

        logger.info(f"重试完成：成功 {len(successful_questions)} 道题目，仍失败 {len(still_failed)} 个片段")
        return successful_questions, still_failed
