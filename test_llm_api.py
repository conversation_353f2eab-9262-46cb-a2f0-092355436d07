"""
LLM API测试脚本
使用提供的API配置测试新模板.ini格式生成
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from llm_service.openai_client import OpenAICompatibleClient
from output_manager.ini_writer import INIWriter

def test_api_connection():
    """测试API连接"""
    print("=" * 60)
    print("测试API连接")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    
    print(f"API地址: {config.API_BASE_URL}")
    print(f"模型: {config.MODEL_NAME}")
    print(f"API密钥: {config.API_KEY[:20]}...")
    
    # 创建LLM客户端
    llm_client = OpenAICompatibleClient(config)
    
    # 测试连接
    try:
        success = llm_client.test_connection()
        if success:
            print("[成功] API连接测试通过")
            return True
        else:
            print("[失败] API连接测试失败")
            return False
    except Exception as e:
        print(f"[错误] API连接异常: {str(e)}")
        return False

def test_ini_generation():
    """测试INI格式题目生成"""
    print("\n" + "=" * 60)
    print("测试新模板.ini格式题目生成")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    config.OUTPUT_DIR = "test_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建LLM客户端和INI写入器
    llm_client = OpenAICompatibleClient(config)
    ini_writer = INIWriter(config)
    
    # 测试文档内容
    test_content = """
新模板.ini格式填写规范说明

一、基本要求
新模板.ini格式是一种标准化的题库格式，支持多种题型的导入和管理。

二、支持的题型
1. 单选题：提供多个选项，只有一个正确答案
2. 多选题：提供多个选项，可以有多个正确答案
3. 判断题：判断陈述的正确性
4. 填空题：在文本中填入缺失的内容
5. 简答题：需要简短回答的问题

三、格式要求
- 题型用【】标识，如【单选题】
- 题目难度分为：简单、一般、较难、困难
- 知识点最多支持5个，用"|"分隔
- 单选和多选题最多支持12个选项（A-L）
- 判断题答案填写"对"或"错"
- 简答题使用关键词进行系统阅卷

四、技术优势
新模板.ini格式具有标准化程度高、兼容性好、易于管理等优点，能够有效提升题库管理效率。
"""
    
    print(f"测试文档内容长度: {len(test_content)} 字符")
    print("开始生成题目...")
    
    try:
        # 生成题目
        quiz_data = llm_client.generate_quiz(
            content=test_content,
            source_filename="新模板格式说明.txt"
        )
        
        if quiz_data and 'questions' in quiz_data:
            questions = quiz_data['questions']
            print(f"[成功] 生成了 {len(questions)} 道题目")
            
            # 显示生成的题目概览
            for i, question in enumerate(questions, 1):
                print(f"  题目{i}: 【{question.get('type', '未知')}题】{question.get('question', '')[:30]}...")
            
            # 保存为INI格式
            ini_file = ini_writer.save_questions_to_ini(questions, "test_generated.ini")
            print(f"[保存] INI文件已保存: {ini_file}")
            
            # 显示文件内容预览
            print(f"\n生成的INI文件内容预览:")
            print("-" * 40)
            try:
                with open(ini_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(content[:1500])  # 显示前1500字符
                if len(content) > 1500:
                    print("...")
                    print(f"[信息] 完整内容请查看: {ini_file}")
            except Exception as e:
                print(f"[错误] 读取文件失败: {str(e)}")
            
            # 生成统计报告
            summary = ini_writer.generate_summary_report(questions)
            print(f"\n[统计] 题目分布:")
            for key, value in summary.items():
                if key != 'total_questions':
                    print(f"  {key}: {value} 道")
            
            return True
            
        elif quiz_data and quiz_data.get('insufficient_content'):
            print("[警告] LLM报告文档内容不足以生成题目")
            return False
        else:
            print("[失败] 未能生成有效题目")
            return False
            
    except Exception as e:
        print(f"[错误] 生成题目失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ini_parsing():
    """测试INI格式解析"""
    print("\n" + "=" * 60)
    print("测试INI格式解析功能")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    llm_client = OpenAICompatibleClient(config)
    
    # 模拟INI格式响应
    test_ini_response = """1.【单选题】新模板.ini格式最多支持多少个选项？
A、4个选项
B、8个选项
C、12个选项
D、16个选项
正确答案：C
题目难度：简单
答案解析：根据《新模板填写规范》，单选和多选题最多支持12个选项（A-L）。
知识点：新模板规范|选项数量

2.【简答题】请简述新模板.ini格式的主要优势。
关键字1：标准化程度/标准化/规范化
关键字2：兼容性好/兼容性/系统兼容
关键字3：易于管理/管理方便/便于管理
题目难度：一般
作答上传图片：否
答案解析：新模板.ini格式具有这些显著优势。
知识点：格式优势|系统特点"""
    
    print("测试INI格式解析...")
    
    try:
        parsed_result = llm_client._parse_ini_response(test_ini_response)
        
        if parsed_result and 'questions' in parsed_result:
            questions = parsed_result['questions']
            print(f"[成功] 解析出 {len(questions)} 道题目")
            
            for i, question in enumerate(questions, 1):
                print(f"\n题目 {i}:")
                print(f"  类型: {question.get('type', 'N/A')}")
                print(f"  题目: {question.get('question', 'N/A')[:50]}...")
                print(f"  答案: {question.get('answer', 'N/A')}")
                print(f"  难度: {question.get('difficulty', 'N/A')}")
                print(f"  知识点: {question.get('knowledge_points', [])}")
            
            return True
        else:
            print("[失败] INI格式解析失败")
            return False
            
    except Exception as e:
        print(f"[错误] 解析测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("LLM API 新模板.ini格式测试")
    print("使用API: http://10.45.131.70:3001")
    print("模型: rsv-rcgjfz7v")
    
    results = []
    
    # 测试1: API连接
    results.append(("API连接测试", test_api_connection()))
    
    # 测试2: INI格式解析
    results.append(("INI格式解析测试", test_ini_parsing()))
    
    # 测试3: 题目生成（只有连接成功才进行）
    if results[0][1]:  # 如果API连接成功
        results.append(("INI格式题目生成测试", test_ini_generation()))
    else:
        print("\n[跳过] 由于API连接失败，跳过题目生成测试")
        results.append(("INI格式题目生成测试", False))
    
    # 显示测试结果总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, success in results:
        status = "[通过]" if success else "[失败]"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("[成功] 所有测试通过！新模板.ini格式功能正常")
    else:
        print("[警告] 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
