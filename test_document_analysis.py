"""
测试文档分析脚本
分析660MW超临界机组输煤运行规程文档内容
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from document_processor.doc_reader import DocumentReader
from document_processor.text_splitter import TextSplitter
from llm_service.openai_client import OpenAICompatibleClient
from output_manager.ini_writer import INIWriter

def analyze_document():
    """分析文档内容"""
    print("=" * 60)
    print("660MW超临界机组输煤运行规程文档分析")
    print("=" * 60)
    
    # 文档路径
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        return None
    
    # 创建文档读取器
    doc_reader = DocumentReader()
    
    try:
        # 读取文档
        print("📖 正在读取文档...")
        doc_info = doc_reader.read_document(doc_path)
        
        print(f"✅ 文档读取成功")
        print(f"📄 文件名: {doc_info['filename']}")
        print(f"📊 内容长度: {len(doc_info['content'])} 字符")
        print(f"📋 段落数量: {doc_info['metadata'].get('paragraphs_count', 'N/A')}")
        print(f"📊 表格数量: {doc_info['metadata'].get('tables_count', 'N/A')}")
        
        # 显示内容预览
        print(f"\n📝 内容预览（前1000字符）:")
        print("-" * 40)
        print(doc_info['content'][:1000])
        if len(doc_info['content']) > 1000:
            print("...")
        print("-" * 40)
        
        return doc_info
        
    except Exception as e:
        print(f"❌ 文档读取失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_text_splitting(doc_info):
    """分析文本分割"""
    print("\n" + "=" * 60)
    print("文本分割分析")
    print("=" * 60)
    
    if not doc_info:
        print("❌ 没有文档信息可供分析")
        return None
    
    # 创建配置
    config = Config()
    
    # 创建文本分割器
    text_splitter = TextSplitter(
        max_chunk_size=config.MAX_CHUNK_SIZE,
        disable_splitting=config.DISABLE_DOCUMENT_SPLITTING,
        enable_chunk_merging=config.ENABLE_CHUNK_MERGING,
        use_new_splitting_logic=getattr(config, 'USE_NEW_SPLITTING_LOGIC', False)
    )
    
    try:
        # 分割文档
        print("✂️ 正在分割文档...")
        chunks = text_splitter.split_document(doc_info)
        
        print(f"✅ 文档分割成功")
        print(f"📊 分割后块数: {len(chunks)}")
        
        # 分析每个块
        for i, chunk in enumerate(chunks):
            print(f"\n📦 块 {i+1}:")
            print(f"  字符数: {chunk['char_count']}")
            print(f"  Token数: {chunk.get('token_count', 'N/A')}")
            print(f"  内容预览: {chunk['content'][:200]}...")
            
            # 检查内容质量
            content = chunk['content'].strip()
            if len(content) < 100:
                print(f"  ⚠️ 内容过短，可能不足以生成题目")
            elif len(content.split()) < 20:
                print(f"  ⚠️ 词汇量较少，可能不足以生成题目")
            else:
                print(f"  ✅ 内容长度适中")
        
        return chunks
        
    except Exception as e:
        print(f"❌ 文档分割失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_llm_generation(chunks):
    """测试LLM生成"""
    print("\n" + "=" * 60)
    print("LLM题目生成测试")
    print("=" * 60)
    
    if not chunks:
        print("❌ 没有文本块可供测试")
        return None
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    config.OUTPUT_DIR = "test_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建LLM客户端
    llm_client = OpenAICompatibleClient(config)
    
    # 测试每个块
    successful_generations = []
    failed_generations = []
    
    for i, chunk in enumerate(chunks):
        print(f"\n🧪 测试块 {i+1}/{len(chunks)}:")
        print(f"  内容长度: {chunk['char_count']} 字符")
        
        try:
            # 生成题目
            quiz_data = llm_client.generate_quiz(
                content=chunk['content'],
                source_filename=chunk['filename']
            )
            
            if quiz_data and 'questions' in quiz_data:
                questions = quiz_data['questions']
                print(f"  ✅ 成功生成 {len(questions)} 道题目")
                successful_generations.append({
                    'chunk_index': i,
                    'questions': questions,
                    'chunk': chunk
                })
                
                # 显示生成的题目概览
                for j, question in enumerate(questions[:3], 1):  # 只显示前3道
                    print(f"    题目{j}: 【{question.get('type', '未知')}】{question.get('question', '')[:50]}...")
                    
            elif quiz_data and quiz_data.get('insufficient_content'):
                print(f"  ⚠️ LLM报告内容不足以生成题目")
                failed_generations.append({
                    'chunk_index': i,
                    'reason': 'insufficient_content',
                    'chunk': chunk
                })
            else:
                print(f"  ❌ 生成失败，未知原因")
                failed_generations.append({
                    'chunk_index': i,
                    'reason': 'unknown_error',
                    'chunk': chunk
                })
                
        except Exception as e:
            print(f"  ❌ 生成异常: {str(e)}")
            failed_generations.append({
                'chunk_index': i,
                'reason': f'exception: {str(e)}',
                'chunk': chunk
            })
    
    # 总结结果
    print(f"\n📊 生成结果总结:")
    print(f"  成功生成: {len(successful_generations)} 个块")
    print(f"  生成失败: {len(failed_generations)} 个块")
    
    if failed_generations:
        print(f"\n❌ 失败原因分析:")
        for failure in failed_generations:
            print(f"  块 {failure['chunk_index']+1}: {failure['reason']}")
    
    return successful_generations, failed_generations

def analyze_content_quality(chunks):
    """分析内容质量"""
    print("\n" + "=" * 60)
    print("内容质量分析")
    print("=" * 60)
    
    if not chunks:
        print("❌ 没有文本块可供分析")
        return
    
    for i, chunk in enumerate(chunks):
        content = chunk['content'].strip()
        print(f"\n📦 块 {i+1} 质量分析:")
        
        # 基本统计
        char_count = len(content)
        word_count = len(content.split())
        line_count = len(content.split('\n'))
        
        print(f"  字符数: {char_count}")
        print(f"  词汇数: {word_count}")
        print(f"  行数: {line_count}")
        
        # 内容类型分析
        if '第' in content and ('条' in content or '章' in content):
            print(f"  ✅ 包含条款结构")
        else:
            print(f"  ⚠️ 缺少明显的条款结构")
            
        if any(keyword in content for keyword in ['规定', '要求', '应当', '必须', '禁止']):
            print(f"  ✅ 包含规范性语言")
        else:
            print(f"  ⚠️ 缺少规范性语言")
            
        if any(keyword in content for keyword in ['输煤', '机组', '运行', '操作', '设备']):
            print(f"  ✅ 包含专业术语")
        else:
            print(f"  ⚠️ 缺少专业术语")
        
        # 内容充实度评估
        if char_count < 500:
            print(f"  ⚠️ 内容过短，可能不足以生成题目")
        elif word_count < 50:
            print(f"  ⚠️ 词汇量不足，可能影响题目质量")
        elif content.count('。') < 3:
            print(f"  ⚠️ 句子数量较少，可能不足以生成多道题目")
        else:
            print(f"  ✅ 内容充实，适合生成题目")

def main():
    """主函数"""
    print("🔍 660MW超临界机组输煤运行规程文档分析工具")
    
    # 步骤1: 分析文档
    doc_info = analyze_document()
    
    if doc_info:
        # 步骤2: 分析文本分割
        chunks = analyze_text_splitting(doc_info)
        
        if chunks:
            # 步骤3: 分析内容质量
            analyze_content_quality(chunks)
            
            # 步骤4: 测试LLM生成
            successful, failed = test_llm_generation(chunks)
            
            # 步骤5: 提供建议
            print("\n" + "=" * 60)
            print("改进建议")
            print("=" * 60)
            
            if failed:
                print("📋 针对生成失败的建议:")
                print("1. 检查文档内容是否包含足够的实质性信息")
                print("2. 确认文档格式是否正确解析")
                print("3. 考虑调整分块大小或合并策略")
                print("4. 检查是否包含足够的专业术语和规范性语言")
                
            if successful:
                print("✅ 部分内容生成成功，建议:")
                print("1. 优化失败块的内容提取")
                print("2. 考虑手动调整分块策略")
                print("3. 检查文档结构是否完整")
        
        print(f"\n🎯 分析完成！")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 分析程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
