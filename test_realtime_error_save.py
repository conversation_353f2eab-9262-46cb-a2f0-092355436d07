"""
测试实时错误保存功能
"""

import os
import sys
import time
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from error_handler.error_manager import ErrorManager

def test_realtime_save():
    """测试实时保存功能"""
    print("=" * 60)
    print("测试实时错误保存功能")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    config.OUTPUT_DIR = "test_output"
    config.ERROR_DIR = "test_error_docs"
    
    # 创建错误管理器
    error_manager = ErrorManager(config)
    
    # 模拟失败的文档片段
    test_chunks = [
        {
            'filename': '附件5：660MW超临界机组输煤运行规程（2024修订）.docx',
            'chunk_index': 0,
            'content': '这是一个测试内容，模拟LLM报告内容不足的情况。内容相对较短，不足以生成高质量的题目。',
            'char_count': 50,
            'token_count': 25
        },
        {
            'filename': '附件5：660MW超临界机组输煤运行规程（2024修订）.docx',
            'chunk_index': 1,
            'content': '另一个测试片段，同样模拟内容不足的情况。这个片段也比较短。',
            'char_count': 35,
            'token_count': 18
        },
        {
            'filename': '测试文档.docx',
            'chunk_index': 0,
            'content': '第三个测试片段，用于验证实时保存功能是否正常工作。',
            'char_count': 30,
            'token_count': 15
        }
    ]
    
    print(f"[测试] 准备测试 {len(test_chunks)} 个失败片段")
    
    saved_files = []
    
    # 逐个测试实时保存
    for i, chunk in enumerate(test_chunks, 1):
        print(f"\n[测试 {i}] 保存失败片段:")
        print(f"  文件: {chunk['filename']}")
        print(f"  片段: {chunk['chunk_index']+1}")
        print(f"  字符数: {chunk['char_count']}")
        
        try:
            # 调用实时保存
            saved_file = error_manager.save_failed_chunk_realtime(
                chunk, 
                "LLM报告文档内容不足以生成题目"
            )
            
            if saved_file and os.path.exists(saved_file):
                print(f"  [成功] 已保存到: {os.path.basename(saved_file)}")
                saved_files.append(saved_file)
            else:
                print(f"  [失败] 保存失败")
                
        except Exception as e:
            print(f"  [错误] 保存异常: {str(e)}")
        
        # 短暂延迟，确保文件名时间戳不同
        time.sleep(0.1)
    
    # 测试统计功能
    print(f"\n[统计] 测试实时保存统计:")
    stats = error_manager.get_realtime_save_statistics()
    
    print(f"  总实时保存次数: {stats['total_realtime_saves']}")
    print(f"  实时保存文件数: {len(stats['realtime_files'])}")
    print(f"  最新保存时间: {stats['latest_save']}")
    print(f"  错误文档目录: {stats['err_docs_dir']}")
    
    # 显示保存的文件列表
    if stats['realtime_files']:
        print(f"\n[文件列表] 实时保存的文件:")
        for file_info in stats['realtime_files'][:5]:  # 只显示前5个
            print(f"  - {file_info['filename']}")
            print(f"    大小: {file_info['size']} 字节")
            print(f"    时间: {file_info['modified']}")
    
    # 验证文件内容
    if saved_files:
        print(f"\n[验证] 检查第一个保存文件的内容:")
        first_file = saved_files[0]
        try:
            with open(first_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"  文件大小: {len(content)} 字符")
                print(f"  内容预览:")
                print("  " + "-" * 40)
                for line in content.split('\n')[:10]:  # 显示前10行
                    print(f"  {line}")
                print("  " + "-" * 40)
        except Exception as e:
            print(f"  [错误] 读取文件失败: {str(e)}")
    
    print(f"\n[完成] 实时保存功能测试完成")
    print(f"  成功保存: {len(saved_files)} 个文件")
    print(f"  保存目录: {error_manager.err_docs_dir}")
    
    return len(saved_files) > 0

def test_with_real_document():
    """使用真实文档测试"""
    print("\n" + "=" * 60)
    print("使用真实文档测试实时保存")
    print("=" * 60)
    
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    if not os.path.exists(doc_path):
        print(f"[跳过] 真实文档不存在: {doc_path}")
        return False
    
    try:
        # 读取真实文档的一小部分
        doc = Document(doc_path)
        content_parts = []
        
        # 只取前几个段落，模拟内容不足的情况
        for i, paragraph in enumerate(doc.paragraphs[:3]):
            text = paragraph.text.strip()
            if text:
                content_parts.append(text)
        
        if not content_parts:
            print("[跳过] 文档前几段为空")
            return False
        
        content = '\n\n'.join(content_parts)
        
        # 创建模拟的失败片段
        real_chunk = {
            'filename': os.path.basename(doc_path),
            'chunk_index': 4,  # 模拟第5个片段
            'content': content,
            'char_count': len(content),
            'token_count': len(content.split())
        }
        
        print(f"[真实测试] 文档片段信息:")
        print(f"  文件: {real_chunk['filename']}")
        print(f"  片段: {real_chunk['chunk_index']+1}")
        print(f"  字符数: {real_chunk['char_count']}")
        print(f"  内容预览: {content[:100]}...")
        
        # 创建错误管理器
        config = Config()
        config.OUTPUT_DIR = "test_output"
        config.ERROR_DIR = "test_error_docs"
        error_manager = ErrorManager(config)
        
        # 实时保存
        saved_file = error_manager.save_failed_chunk_realtime(
            real_chunk,
            "真实文档测试 - LLM报告内容不足以生成题目"
        )
        
        if saved_file and os.path.exists(saved_file):
            print(f"[成功] 真实文档片段已保存: {os.path.basename(saved_file)}")
            return True
        else:
            print(f"[失败] 真实文档片段保存失败")
            return False
            
    except Exception as e:
        print(f"[错误] 真实文档测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("[测试] 实时错误保存功能测试")
    
    # 测试1: 基本实时保存功能
    basic_test_success = test_realtime_save()
    
    # 测试2: 真实文档测试
    real_doc_test_success = test_with_real_document()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"基本功能测试: {'✅ 通过' if basic_test_success else '❌ 失败'}")
    print(f"真实文档测试: {'✅ 通过' if real_doc_test_success else '❌ 失败'}")
    
    if basic_test_success:
        print(f"\n[成功] 实时错误保存功能正常工作")
        print(f"[说明] 当LLM报告内容不足时，失败片段会立即保存到 err-docs 目录")
        print(f"[用途] 可以用于分析哪些内容无法生成题目，便于优化处理策略")
    else:
        print(f"\n[失败] 实时错误保存功能存在问题")
        print(f"[建议] 检查错误管理器的实现和文件权限")
    
    print(f"\n[完成] 测试结束")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
