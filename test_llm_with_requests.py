"""
使用requests直接测试LLM API和新模板.ini格式生成
"""

import os
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from output_manager.ini_writer import INIWriter
from llm_service.openai_client import OpenAICompatibleClient

def test_api_with_requests():
    """使用requests直接测试API"""
    print("=" * 60)
    print("使用requests直接测试API")
    print("=" * 60)
    
    api_url = "http://10.45.131.70:3001/v1/chat/completions"
    api_key = "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3"
    model = "rsv-rcgjfz7v"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试INI格式生成的提示词
    prompt = """请根据以下文档内容生成题目，严格按照新模板.ini格式要求。要求生成以下数量的题目：
- 单选题：1道
- 简答题：1道

**新模板.ini格式填写规范：**
1. 题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】
2. 题目难度：简单/一般/较难/困难
3. 简答题特殊要求：关键词用作系统阅卷使用，关键词应该是浓缩的核心概念，而不是单个字

**输出格式要求**：
请直接输出INI格式的题目，格式如下：

1.【单选题】题目内容
A、选项内容
B、选项内容  
C、选项内容
D、选项内容
正确答案：A
题目难度：简单
答案解析：解析内容
知识点：知识点1|知识点2

2.【简答题】题目内容
关键字1：核心概念1/主要内容1/重点要素1
关键字2：核心概念2/主要内容2/重点要素2
题目难度：一般
作答上传图片：否
答案解析：解析内容
知识点：知识点1|知识点2

文档内容：
新模板.ini格式说明：
1. 支持单选题、多选题、判断题、填空题、简答题
2. 题目难度分为：简单、一般、较难、困难
3. 知识点最多支持5个，用"|"分隔
4. 简答题使用关键词格式进行阅卷，关键词应该是核心概念而不是单个字
5. 新模板具有标准化程度高、兼容性好、易于管理等优点
"""
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的题目生成助手。请严格按照要求的格式输出，不要添加额外的说明文字。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "max_tokens": 2000,
        "temperature": 0.7
    }
    
    try:
        print("发送API请求...")
        response = requests.post(api_url, headers=headers, json=data, timeout=60)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_json = response.json()
            content = response_json['choices'][0]['message']['content']
            
            print("API调用成功！")
            print("生成的内容:")
            print("-" * 40)
            print(content)
            print("-" * 40)
            
            return content
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"API调用异常: {str(e)}")
        return None

def test_ini_parsing_and_saving(content):
    """测试INI格式解析和保存"""
    print("\n" + "=" * 60)
    print("测试INI格式解析和保存")
    print("=" * 60)
    
    if not content:
        print("没有内容可供解析")
        return False
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    config.OUTPUT_DIR = "test_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建LLM客户端和INI写入器
    llm_client = OpenAICompatibleClient(config)
    ini_writer = INIWriter(config)
    
    try:
        # 解析INI格式
        parsed_result = llm_client._parse_ini_response(content)
        
        if parsed_result and 'questions' in parsed_result:
            questions = parsed_result['questions']
            print(f"[成功] 解析出 {len(questions)} 道题目")
            
            # 显示解析结果
            for i, question in enumerate(questions, 1):
                print(f"\n题目 {i}:")
                print(f"  类型: {question.get('type', 'N/A')}")
                print(f"  题目: {question.get('question', 'N/A')[:50]}...")
                print(f"  答案: {question.get('answer', 'N/A')}")
                print(f"  难度: {question.get('difficulty', 'N/A')}")
                print(f"  知识点: {question.get('knowledge_points', [])}")
            
            # 保存为INI文件
            ini_file = ini_writer.save_questions_to_ini(questions, "test_llm_generated.ini")
            print(f"\n[保存] INI文件已保存: {ini_file}")
            
            # 显示保存的文件内容
            print(f"\n保存的INI文件内容:")
            print("-" * 40)
            try:
                with open(ini_file, 'r', encoding='utf-8') as f:
                    saved_content = f.read()
                print(saved_content[:1000])  # 显示前1000字符
                if len(saved_content) > 1000:
                    print("...")
                    print(f"[信息] 完整内容请查看: {ini_file}")
            except Exception as e:
                print(f"[错误] 读取保存的文件失败: {str(e)}")
            
            return True
        else:
            print("[失败] 无法解析INI格式")
            return False
            
    except Exception as e:
        print(f"[错误] 解析和保存失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n" + "=" * 60)
    print("测试完整工作流程")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    config.OUTPUT_DIR = "test_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建LLM客户端和INI写入器
    llm_client = OpenAICompatibleClient(config)
    ini_writer = INIWriter(config)
    
    # 测试文档内容
    test_content = """
新模板.ini格式技术规范

一、概述
新模板.ini格式是专为题库管理设计的标准化格式，具有以下特点：
1. 支持多种题型：单选题、多选题、判断题、填空题、简答题
2. 标准化程度高，便于系统处理
3. 兼容性好，支持各种题库系统导入

二、技术特性
1. 题目难度分级：简单、一般、较难、困难
2. 知识点标记：最多支持5个知识点，用"|"分隔
3. 简答题关键词：用于系统自动阅卷，关键词应为核心概念

三、应用优势
新模板.ini格式相比传统格式具有显著优势：
- 提高了题库管理效率
- 降低了系统维护成本
- 增强了数据交换能力
- 支持智能化阅卷功能
"""
    
    try:
        print("开始生成题目...")
        
        # 使用LLM客户端生成题目
        quiz_data = llm_client.generate_quiz(
            content=test_content,
            source_filename="新模板技术规范.txt"
        )
        
        if quiz_data and 'questions' in quiz_data:
            questions = quiz_data['questions']
            print(f"[成功] 生成了 {len(questions)} 道题目")
            
            # 保存为INI文件
            ini_file = ini_writer.save_questions_to_ini(questions, "test_complete_workflow.ini")
            print(f"[保存] 完整工作流程生成的INI文件: {ini_file}")
            
            # 生成统计报告
            summary = ini_writer.generate_summary_report(questions)
            print(f"\n[统计] 题目分布:")
            for key, value in summary.items():
                if key != 'total_questions':
                    print(f"  {key}: {value} 道")
            
            return True
        else:
            print("[失败] 完整工作流程生成题目失败")
            return False
            
    except Exception as e:
        print(f"[错误] 完整工作流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("LLM API 新模板.ini格式完整测试")
    print("API: http://10.45.131.70:3001")
    print("模型: rsv-rcgjfz7v")
    
    results = []
    
    # 测试1: 直接API调用
    content = test_api_with_requests()
    results.append(("直接API调用", content is not None))
    
    # 测试2: INI格式解析和保存
    if content:
        parse_success = test_ini_parsing_and_saving(content)
        results.append(("INI格式解析和保存", parse_success))
    else:
        print("\n[跳过] 由于API调用失败，跳过INI格式解析测试")
        results.append(("INI格式解析和保存", False))
    
    # 测试3: 完整工作流程
    if content:
        workflow_success = test_complete_workflow()
        results.append(("完整工作流程", workflow_success))
    else:
        print("\n[跳过] 由于API调用失败，跳过完整工作流程测试")
        results.append(("完整工作流程", False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, success in results:
        status = "[通过]" if success else "[失败]"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("[成功] 所有测试通过！新模板.ini格式功能完全正常")
    elif passed > 0:
        print("[部分成功] 部分功能正常，请检查失败的测试项")
    else:
        print("[失败] 所有测试失败，请检查API配置和网络连接")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
