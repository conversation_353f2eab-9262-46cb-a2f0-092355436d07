"""
简单的API测试脚本
直接测试API调用和响应处理
"""

import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from llm_service.openai_client import OpenAICompatibleClient

def test_direct_api_call():
    """直接测试API调用"""
    print("=" * 60)
    print("直接API调用测试")
    print("=" * 60)
    
    api_url = "http://10.45.131.70:3001/v1/chat/completions"
    api_key = "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3"
    model = "rsv-rcgjfz7v"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "请回复'测试成功'"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        print(f"请求URL: {api_url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(api_url, headers=headers, json=data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                response_json = response.json()
                print(f"解析后的JSON: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
                return True
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                return False
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"API调用异常: {str(e)}")
        return False

def test_llm_client():
    """测试LLM客户端"""
    print("\n" + "=" * 60)
    print("LLM客户端测试")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    
    # 创建LLM客户端
    llm_client = OpenAICompatibleClient(config)
    
    try:
        # 测试简单调用
        print("测试简单API调用...")
        response = llm_client._make_api_call("请回复'LLM客户端测试成功'")
        print(f"响应: {response}")
        
        if response and "成功" in response:
            print("[成功] LLM客户端调用正常")
            return True
        else:
            print("[失败] LLM客户端调用异常")
            return False
            
    except Exception as e:
        print(f"[错误] LLM客户端测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ini_generation_simple():
    """测试简单的INI格式生成"""
    print("\n" + "=" * 60)
    print("简单INI格式生成测试")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    
    # 创建LLM客户端
    llm_client = OpenAICompatibleClient(config)
    
    # 简单的测试内容
    test_content = """
新模板.ini格式说明：
1. 支持单选题、多选题、判断题、填空题、简答题
2. 题目难度分为：简单、一般、较难、困难
3. 知识点最多支持5个，用"|"分隔
4. 简答题使用关键词格式进行阅卷
"""
    
    try:
        print("开始生成题目...")
        
        # 直接调用API
        prompt = f"""请根据以下文档内容生成题目，严格按照新模板.ini格式要求。要求生成以下数量的题目：
- 单选题：1道
- 简答题：1道

**新模板.ini格式填写规范：**
1. 题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】
2. 题目难度：简单/一般/较难/困难
3. 简答题特殊要求：关键词用作系统阅卷使用，关键词应该是浓缩的核心概念，而不是单个字

**输出格式要求**：
请直接输出INI格式的题目，格式如下：

1.【单选题】题目内容
A、选项内容
B、选项内容  
C、选项内容
D、选项内容
正确答案：A
题目难度：简单
答案解析：解析内容
知识点：知识点1|知识点2

2.【简答题】题目内容
关键字1：核心概念1/主要内容1/重点要素1
关键字2：核心概念2/主要内容2/重点要素2
题目难度：一般
作答上传图片：否
答案解析：解析内容
知识点：知识点1|知识点2

文档内容：
{test_content}
"""
        
        response = llm_client._make_api_call(prompt)
        print(f"API响应: {response}")
        
        if response:
            # 尝试解析INI格式
            parsed_result = llm_client._parse_ini_response(response)
            
            if parsed_result and 'questions' in parsed_result:
                questions = parsed_result['questions']
                print(f"[成功] 解析出 {len(questions)} 道题目")
                
                for i, question in enumerate(questions, 1):
                    print(f"\n题目 {i}:")
                    print(f"  类型: {question.get('type', 'N/A')}")
                    print(f"  题目: {question.get('question', 'N/A')[:50]}...")
                    print(f"  答案: {question.get('answer', 'N/A')}")
                    print(f"  难度: {question.get('difficulty', 'N/A')}")
                
                return True
            else:
                print("[失败] 无法解析INI格式")
                return False
        else:
            print("[失败] API调用无响应")
            return False
            
    except Exception as e:
        print(f"[错误] 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("简单API测试")
    print("API: http://10.45.131.70:3001")
    print("模型: rsv-rcgjfz7v")
    
    results = []
    
    # 测试1: 直接API调用
    results.append(("直接API调用", test_direct_api_call()))
    
    # 测试2: LLM客户端
    results.append(("LLM客户端测试", test_llm_client()))
    
    # 测试3: INI格式生成（只有前面成功才进行）
    if results[1][1]:  # 如果LLM客户端测试成功
        results.append(("INI格式生成测试", test_ini_generation_simple()))
    else:
        print("\n[跳过] 由于LLM客户端测试失败，跳过INI格式生成测试")
        results.append(("INI格式生成测试", False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, success in results:
        status = "[通过]" if success else "[失败]"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 项测试通过")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 测试程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
